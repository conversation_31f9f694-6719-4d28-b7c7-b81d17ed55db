% 绘制X.fActPosition和Y.fActPosition的轨迹图
clear all;
close all;

% 提示用户选择数据文件
disp('请选择要分析的数据文件（.txt 格式）：');
[filename, pathname] = uigetfile('G代码/*.txt', '选择数据文件');

% 如果用户取消了选择，则退出脚本
if filename == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath = fullfile(pathname, filename);

% 读取数据文件
try
    data = readtable(fullPath, 'HeaderLines', 2, 'Delimiter', ' ', 'MultipleDelimsAsOne', true);
    disp(['成功读取文件: ', fullPath]);
catch ME
    disp(['无法读取文件: ', fullPath]);
    disp(['错误信息: ', ME.message]);
    return;
end

% 获取列名
colNames = data.Properties.VariableNames;
disp('可用的列名:');
disp(colNames);

% 找到X.fActPosition和Y.fActPosition的列索引
xColIndex = find(contains(colNames, 'X.fActPosition'));
yColIndex = find(contains(colNames, 'Y.fActPosition'));

% 如果找不到列，尝试其他可能的名称
if isempty(xColIndex) || isempty(yColIndex)
    % 尝试手动指定列索引（根据数据文件中的位置）
    fprintf('未能自动找到X.fActPosition和Y.fActPosition列，尝试手动指定列...\n');
    
    % 根据Data.txt文件中的位置，X.fActPosition应该是第18列，Y.fActPosition是第19列
    xColIndex = 18;
    yColIndex = 19;
    
    fprintf('手动指定X.fActPosition为第%d列，Y.fActPosition为第%d列\n', xColIndex, yColIndex);
end

% 提取X和Y坐标
x = table2array(data(:, xColIndex));
y = table2array(data(:, yColIndex));

% 移除零值点（通常是机床未启动的数据）
valid_indices = (x ~= 0) | (y ~= 0);
x = x(valid_indices);
y = y(valid_indices);

% 绘制轨迹图
figure('Name', ['轨迹图: ', filename], 'NumberTitle', 'off');
plot(x, y, 'b-', 'LineWidth', 1.5);
grid on;
axis equal;  % 确保X轴和Y轴使用相同的比例尺
xlabel('X.fActPosition');
ylabel('Y.fActPosition');
title(['文件: ', filename, ' 的XY轨迹图']);

% 添加起点和终点标记
hold on;
plot(x(1), y(1), 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'g'); % 起点，绿色
plot(x(end), y(end), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r'); % 终点，红色
legend('轨迹', '起点', '终点', 'Location', 'best');


% 显示一些基本统计信息
fprintf('数据点数量: %d\n', length(x));
fprintf('X坐标范围: [%.4f, %.4f]\n', min(x), max(x));
fprintf('Y坐标范围: [%.4f, %.4f]\n', min(y), max(y)); 