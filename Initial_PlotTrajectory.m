% 绘制轨迹图的脚本
function Initial_PlotTrajectory()
%% 选择并读取轨迹文件
disp('请选择包含轨迹数据的文件（.txt 格式）：');
[filename, pathname] = uigetfile('*.txt', '选择轨迹文件');

if filename == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath = fullfile(pathname, filename);

% 打开文件
fileID = fopen(fullPath, 'r');
if fileID == -1
    disp('无法打开文件');
    return;
end

% 初始化路径段数组
segments = [];
current_point = [0, 0]; % 当前点坐标

% 按行读取文件内容，并解析G代码
while ~feof(fileID)
    % 读取一行数据
    line = fgetl(fileID);
    if ~ischar(line) 
        continue;
    end
    
    % 去除前后空格
    line = strtrim(line);
    if isempty(line)
        continue;
    end
    
    % 检测G代码类型
    is_g01 = ~isempty(regexp(line, 'G0*1', 'once'));
    is_g02 = ~isempty(regexp(line, 'G0*2', 'once'));
    is_g03 = ~isempty(regexp(line, 'G0*3', 'once'));
    
    % 提取坐标
    x_match = regexp(line, 'X([-\d.]+)', 'tokens');
    y_match = regexp(line, 'Y([-\d.]+)', 'tokens');
    
    % 提取圆弧参数
    r_match = regexp(line, 'R([-\d.]+)', 'tokens');
    i_match = regexp(line, 'I([-\d.]+)', 'tokens');
    j_match = regexp(line, 'J([-\d.]+)', 'tokens');
    
    if ~isempty(x_match) && ~isempty(y_match)
        x = str2double(x_match{1}{1});
        y = str2double(y_match{1}{1});
        
        % 创建新的路径段 - 预先定义所有可能的字段以确保结构体一致性
        new_segment = struct('type', 0, ...
                           'start_point', current_point, ...
                           'end_point', [x, y], ...
                           'direction', 0, ...
                           'radius', 0, ...
                           'center', [0, 0], ...
                           'start_angle', 0, ...
                           'end_angle', 0, ...
                           'sweep_angle', 0);
        
        if is_g01 % 直线段
            new_segment.type = 1; % 直线类型
            
        elseif is_g02 || is_g03 % 圆弧段
            new_segment.type = 2; % 圆弧类型
            new_segment.direction = is_g02 + 2 * is_g03; % 1表示顺时针(G02)，2表示逆时针(G03)
            
            % 确定圆心
            if ~isempty(r_match) % 使用半径R格式
                r = str2double(r_match{1}{1});
                new_segment.radius = abs(r);
                
                % 计算圆心
                [center_x, center_y] = calculateArcCenter(current_point(1), current_point(2), x, y, r, new_segment.direction);
                new_segment.center = [center_x, center_y];
                
            elseif ~isempty(i_match) && ~isempty(j_match) % 使用I,J格式
                i_val = str2double(i_match{1}{1});
                j_val = str2double(j_match{1}{1});
                
                % 圆心是相对于起点的偏移
                center_x = current_point(1) + i_val;
                center_y = current_point(2) + j_val;
                new_segment.center = [center_x, center_y];
                
                % 计算半径
                new_segment.radius = sqrt((center_x - current_point(1))^2 + (center_y - current_point(2))^2);
            end
            
            % 计算起点和终点相对于圆心的角度
            start_angle = atan2(current_point(2) - new_segment.center(2), current_point(1) - new_segment.center(1));
            end_angle = atan2(y - new_segment.center(2), x - new_segment.center(1));
            
            % 确保角度在正确的范围内
            if new_segment.direction == 1 % 顺时针
                if end_angle > start_angle
                    end_angle = end_angle - 2*pi;
                end
            else % 逆时针
                if end_angle < start_angle
                    end_angle = end_angle + 2*pi;
                end
            end
            
            new_segment.start_angle = start_angle;
            new_segment.end_angle = end_angle;
            new_segment.sweep_angle = abs(end_angle - start_angle);
        end
        
        % 添加新段到数组
        if isempty(segments)
            segments = new_segment;
        else
            segments(end+1) = new_segment;
        end
        
        % 更新当前点
        current_point = [x, y];
    end
end

% 关闭文件
fclose(fileID);

% 检查是否成功读取路径段
if isempty(segments)
    disp('文件中没有有效的路径段数据');
    return;
else
    disp('文件读取成功');
    disp(['提取的路径段数量: ', num2str(length(segments))]);
end

%% 计算轨迹特性
% 提取所有路径点（包括直线段和圆弧段的端点）
all_points = zeros(2, length(segments) + 1);
all_points(:, 1) = segments(1).start_point';

for i = 1:length(segments)
    all_points(:, i + 1) = segments(i).end_point';
end

% 寻找直线与直线的交点（拐角）
corners = [];
for i = 1:length(segments)-1
    if segments(i).type == 1 && segments(i+1).type == 1
        % 这是一个直线-直线的连接点
        corner_point = segments(i).end_point;
        
        % 计算拐角角度
        v1 = segments(i).start_point - corner_point;
        v2 = segments(i+1).end_point - corner_point;
        
        % 计算两个向量之间的夹角（弧度）
        cos_angle = (v1 * v2') / (norm(v1) * norm(v2));
        % 确保cos_angle在[-1, 1]范围内，避免数值误差
        cos_angle = max(min(cos_angle, 1), -1);
        angle = acos(cos_angle);
        
        % 将弧度转换为角度
        angle_degrees = angle * 180 / pi;
        
        % 添加到拐角数组
        if isempty(corners)
            corners = [i, corner_point, angle_degrees];
        else
            corners(end+1, :) = [i, corner_point, angle_degrees];
        end
    end
end

%% 寻找直线与圆弧的连接点
connection_points = [];
for i = 1:length(segments)-1
    if (segments(i).type == 1 && segments(i+1).type == 2) || ...
       (segments(i).type == 2 && segments(i+1).type == 1)
        % 这是一个直线-圆弧或圆弧-直线的连接点
        connection_point = segments(i).end_point;
        
        % 如果是第一个连接点，直接赋值
        if isempty(connection_points)
            connection_points = [i, connection_point];
        else
            % 否则添加到现有数组
            connection_points(end+1,:) = [i, connection_point];
        end
    end
end

%% 绘制轨迹图
figure('Name', ['轨迹图: ', filename], 'NumberTitle', 'off', 'Position', [100, 100, 800, 600]);
hold on;
grid on;
axis equal;

% 绘制所有路径段
for i = 1:length(segments)
    segment = segments(i);
    if segment.type == 1 % 直线段
        plot([segment.start_point(1), segment.end_point(1)], ...
             [segment.start_point(2), segment.end_point(2)], 'b-', 'LineWidth', 1.5);
    else % 圆弧段
        % 计算圆弧点
        angles = linspace(segment.start_angle, segment.end_angle, 100);
        arc_x = segment.center(1) + segment.radius * cos(angles);
        arc_y = segment.center(2) + segment.radius * sin(angles);
        plot(arc_x, arc_y, 'b-', 'LineWidth', 1.5);
        
        % 标记圆心（可选）
        plot(segment.center(1), segment.center(2), 'kx', 'MarkerSize', 6);
        
        % 绘制从圆心到起点和终点的连线（可选，帮助可视化）
        plot([segment.center(1), segment.start_point(1)], ...
             [segment.center(2), segment.start_point(2)], 'k:', 'LineWidth', 0.5);
        plot([segment.center(1), segment.end_point(1)], ...
             [segment.center(2), segment.end_point(2)], 'k:', 'LineWidth', 0.5);
    end
    
    % 标记起点（仅第一个段）
    if i == 1
        plot(segment.start_point(1), segment.start_point(2), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
        text(segment.start_point(1), segment.start_point(2), '  起点', 'FontSize', 12, 'Color', 'k');
    end
    
    % 标记终点（仅最后一个段）
    if i == length(segments)
        plot(segment.end_point(1), segment.end_point(2), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
        text(segment.end_point(1), segment.end_point(2), '  终点', 'FontSize', 12, 'Color', 'k');
    end
end

% 标记所有路径点
for i = 1:size(all_points, 2)
    plot(all_points(1, i), all_points(2, i), 'ro', 'MarkerSize', 6);
end

% 标记直线-直线拐角和角度
for i = 1:size(corners, 1)
    corner_idx = corners(i, 1);
    corner_x = corners(i, 2);
    corner_y = corners(i, 3);
    angle_degrees = corners(i, 4);
    
    % 用不同颜色标记大于170度的拐角
    if angle_degrees > 170
        plot(corner_x, corner_y, 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'g');
    else
        plot(corner_x, corner_y, 'mo', 'MarkerSize', 8, 'MarkerFaceColor', 'm');
    end
    
    % 添加角度标签
    text(corner_x, corner_y, sprintf('  %.1f°', angle_degrees), 'FontSize', 10);
end

% 标记直线-圆弧连接点
for i = 1:size(connection_points, 1)
    segment_idx = connection_points(i, 1);
    point_x = connection_points(i, 2);
    point_y = connection_points(i, 3);
    
    % 标记连接点
    plot(point_x, point_y, 'gs', 'MarkerSize', 10, 'MarkerFaceColor', 'g');
    
    % 确定哪个是直线段，哪个是圆弧段
    if segments(segment_idx).type == 1 % 当前是直线段，下一个是圆弧段
        text(point_x, point_y, '  直→圆', 'FontSize', 10, 'Color', 'b');
    else % 当前是圆弧段，下一个是直线段
        text(point_x, point_y, '  圆→直', 'FontSize', 10, 'Color', 'b');
    end
end

% 添加图例
legend_items = {'轨迹线', '路径点', '圆心', '圆弧半径'};
if ~isempty(corners)
    legend_items = [legend_items, {'大于170°的拐角', '小于等于170°的拐角'}];
end
if ~isempty(connection_points)
    legend_items = [legend_items, {'直线-圆弧连接点'}];
end
legend(legend_items, 'Location', 'Best');

% 设置标题和标签
title(['文件: ', filename, ' 的轨迹图'], 'FontSize', 14);
xlabel('X 坐标', 'FontSize', 12);
ylabel('Y 坐标', 'FontSize', 12);

% 添加标注说明
annotation('textbox', [0.15, 0.02, 0.7, 0.05], 'String', ...
    '绿色点: 角度>170°的拐角(不需要光顺) | 紫色点: 角度≤170°的拐角(需要光顺) | 绿色方块: 直线-圆弧连接点', ...
    'EdgeColor', 'none', 'HorizontalAlignment', 'center', 'FontSize', 10);

end

%% 计算圆弧中心点
function [center_x, center_y] = calculateArcCenter(x1, y1, x2, y2, r, direction)
% 根据起点、终点、半径和方向计算圆弧中心点
% 输入:
%   x1, y1: 起点坐标
%   x2, y2: 终点坐标
%   r: 半径
%   direction: 1表示顺时针(G02)，2表示逆时针(G03)
% 输出:
%   center_x, center_y: 圆心坐标

% 计算起点和终点之间的中点
mid_x = (x1 + x2) / 2;
mid_y = (y1 + y2) / 2;

% 计算起点和终点之间的距离
chord = sqrt((x2 - x1)^2 + (y2 - y1)^2);

% 检查半径是否足够大
if abs(r) < chord/2
    error('半径太小，无法创建圆弧');
end

% 计算从中点到圆心的距离
h = sqrt(r^2 - (chord/2)^2);

% 计算起点到终点的向量
dx = x2 - x1;
dy = y2 - y1;

% 单位化
len = sqrt(dx^2 + dy^2);
dx = dx / len;
dy = dy / len;

% 计算垂直于起点到终点向量的单位向量
% 对于G02(顺时针)和G03(逆时针)，垂直方向相反
if direction == 1 % 顺时针(G02)
    nx = dy;   % 向右旋转90度
    ny = -dx;
else % 逆时针(G03)
    nx = -dy;  % 向左旋转90度
    ny = dx;
end

% 计算圆心
center_x = mid_x + h * nx;
center_y = mid_y + h * ny;

% 验证计算结果
r1 = sqrt((center_x - x1)^2 + (center_y - y1)^2);
r2 = sqrt((center_x - x2)^2 + (center_y - y2)^2);

% 检查计算的圆心是否满足条件
if abs(r1 - abs(r)) > 1e-6 || abs(r2 - abs(r)) > 1e-6
    warning('计算的圆心可能不准确，请检查。');
    fprintf('计算的半径: r1=%.6f, r2=%.6f, 期望半径: r=%.6f\n', r1, r2, abs(r));
    
    % 尝试调整圆心位置，使其满足半径条件
    % 计算圆心到起点和终点的单位向量
    v1 = [x1 - center_x, y1 - center_y] / r1;
    v2 = [x2 - center_x, y2 - center_y] / r2;
    
    % 调整圆心位置
    center_x = (x1 - r * v1(1) + x2 - r * v2(1)) / 2;
    center_y = (y1 - r * v1(2) + y2 - r * v2(2)) / 2;
    
    % 重新验证
    r1 = sqrt((center_x - x1)^2 + (center_y - y1)^2);
    r2 = sqrt((center_x - x2)^2 + (center_y - y2)^2);
    fprintf('调整后的半径: r1=%.6f, r2=%.6f\n', r1, r2);
end

% 验证方向是否正确
% 计算起点和终点相对于圆心的角度
angle1 = atan2(y1 - center_y, x1 - center_x);
angle2 = atan2(y2 - center_y, x2 - center_x);

% 根据方向调整角度，使其在正确的范围内
if direction == 1 % 顺时针(G02)
    if angle2 > angle1
        angle2 = angle2 - 2*pi;
    end
    sweep_angle = angle1 - angle2;
else % 逆时针(G03)
    if angle2 < angle1
        angle2 = angle2 + 2*pi;
    end
    sweep_angle = angle2 - angle1;
end

% 如果计算出的弧度小于0或大于π，可能圆心方向错误
if sweep_angle < 0 || sweep_angle > pi
    % 尝试反向计算
    center_x = mid_x - h * nx;
    center_y = mid_y - h * ny;
    
    % 重新验证
    r1 = sqrt((center_x - x1)^2 + (center_y - y1)^2);
    r2 = sqrt((center_x - x2)^2 + (center_y - y2)^2);
    
    if abs(r1 - abs(r)) > 1e-6 || abs(r2 - abs(r)) > 1e-6
        warning('反向计算的圆心也可能不准确，请检查。');
    end
end
end 