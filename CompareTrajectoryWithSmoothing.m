function CompareTrajectoryWithSmoothing()
% 对比机床实际路径与系统光顺路径
% 结合Initial_PlotTrajectory和NewCompleteSmoothPath的功能
% 在同一坐标轴下显示机床实际路径和系统光顺路径

clear all;
close all;

%% 第一步：选择显示模式
disp('请选择显示模式:');
disp('1. 仅显示机床实际路径');
disp('2. 显示G代码路径和光顺路径');
disp('3. 仅显示光顺路径');
disp('4. 显示所有路径（默认）');

display_mode = input('请输入选择 (1-4): ');
if isempty(display_mode)
    display_mode = 4; % 默认显示所有路径
end

%% 第二步：根据显示模式读取相应的数据文件
% 只有在需要显示机床实际路径时才读取机床数据
if display_mode == 1 || display_mode == 4
    disp('请选择机床实际路径数据文件（.txt 格式）：');
    [filename_machine, pathname_machine] = uigetfile('*.txt', '选择机床实际路径数据');

    if filename_machine == 0
        disp('用户取消了机床数据文件选择');
        if display_mode == 1 % 如果只显示机床路径，则退出
            return;
        else
            % 如果是显示所有路径，则改为只显示G代码和光顺路径
            display_mode = 3;
            disp('将只显示G代码和光顺路径');
        end
    else
        % 获取文件的完整路径
        fullPath_machine = fullfile(pathname_machine, filename_machine);

        % 读取机床数据文件
        try
            machine_data = readtable(fullPath_machine, 'HeaderLines', 2, 'Delimiter', ' ', 'MultipleDelimsAsOne', true);
            disp(['成功读取机床数据文件: ', fullPath_machine]);
        catch ME
            disp(['无法读取文件: ', fullPath_machine]);
            disp(['错误信息: ', ME.message]);
            
            % 尝试使用低级文件读取方式
            try
                fileID = fopen(fullPath_machine, 'r');
                if fileID == -1
                    error('无法打开文件');
                end
                
                % 跳过前两行
                fgetl(fileID);
                fgetl(fileID);
                
                % 初始化数据数组
                x_machine = [];
                y_machine = [];
                
                % 逐行读取数据
                while ~feof(fileID)
                    line = fgetl(fileID);
                    if ischar(line)
                        % 使用正则表达式或字符串分割提取数据
                        parts = strsplit(line);
                        if length(parts) >= 19  % 假设X在第18列，Y在第19列
                            try
                                x = str2double(parts{18});
                                y = str2double(parts{19});
                                if ~isnan(x) && ~isnan(y)
                                    x_machine = [x_machine; x];
                                    y_machine = [y_machine; y];
                                end
                            catch
                                continue;
                            end
                        end
                    end
                end
                
                fclose(fileID);
                
                if isempty(x_machine) || isempty(y_machine)
                    error('未能从文件中提取有效数据');
                else
                    disp('使用备用方法成功读取数据');
                end
            catch ME2
                disp(['备用读取方法也失败: ', ME2.message]);
                if display_mode == 1 % 如果只显示机床路径，则退出
                    return;
                else
                    % 如果是显示所有路径，则改为只显示G代码和光顺路径
                    display_mode = 3;
                    disp('将只显示G代码和光顺路径');
                end
            end
        end

        % 如果使用readtable成功，提取X和Y坐标
        if exist('machine_data', 'var')
            % 获取列名
            colNames = machine_data.Properties.VariableNames;
            
            % 尝试找到X和Y位置数据列
            xColIndex = find(contains(colNames, 'X.fActPosition'));
            yColIndex = find(contains(colNames, 'Y.fActPosition'));
            
            % 如果找不到列，尝试手动指定
            if isempty(xColIndex) || isempty(yColIndex)
                % 尝试手动指定列索引（根据数据文件中的位置）
                fprintf('未能自动找到X.fActPosition和Y.fActPosition列，尝试手动指定列...\n');
                
                % 根据Data.txt文件中的位置，X.fActPosition应该是第18列，Y.fActPosition是第19列
                xColIndex = 18;
                yColIndex = 19;
                
                fprintf('手动指定X.fActPosition为第%d列，Y.fActPosition为第%d列\n', xColIndex, yColIndex);
            end
            
            % 提取X和Y坐标
            x_machine = table2array(machine_data(:, xColIndex));
            y_machine = table2array(machine_data(:, yColIndex));
        end

        % 移除零值点（通常是机床未启动的数据）
        valid_indices = (x_machine ~= 0) | (y_machine ~= 0);
        x_machine = x_machine(valid_indices);
        y_machine = y_machine(valid_indices);
    end
else
    % 如果不需要显示机床路径，设置为空数组
    x_machine = [];
    y_machine = [];
    filename_machine = 'no_machine_file';
end

%% 第三步：读取G代码文件
% 只有在需要显示G代码路径或光顺路径时才读取G代码
if display_mode >= 2
    disp(' ');
    disp('请选择包含路径数据的G代码文件（.txt 格式）：');
    [filename_gcode, pathname_gcode] = uigetfile('*.txt', '选择G代码文件');

    if filename_gcode == 0
        disp('用户取消了G代码文件选择');
        if display_mode == 2 || display_mode == 3 % 如果只显示G代码或光顺路径，则退出
            return;
        else
            % 如果是显示所有路径，则改为只显示机床路径
            display_mode = 1;
            disp('将只显示机床实际路径');
        end
    else
        % 获取文件的完整路径
        fullPath_gcode = fullfile(pathname_gcode, filename_gcode);

        % 打开文件
        fileID = fopen(fullPath_gcode, 'r');
        if fileID == -1
            disp('无法打开文件');
            if display_mode == 2 || display_mode == 3 % 如果只显示G代码或光顺路径，则退出
                return;
            else
                % 如果是显示所有路径，则改为只显示机床路径
                display_mode = 1;
                disp('将只显示机床实际路径');
            end
        else
            % 初始化路径段数组
            segments = [];
            segment_types = []; % 1表示直线段，2表示圆弧段
            current_point = [0, 0]; % 当前点坐标

            % 按行读取文件内容，并解析G代码
            while ~feof(fileID)
                % 读取一行数据
                line = fgetl(fileID);
                if ~ischar(line) 
                    continue;
                end
                
                % 去除前后空格
                line = strtrim(line);
                if isempty(line)
                    continue;
                end
                
                % 检测G代码类型
                is_g01 = ~isempty(regexp(line, 'G0*1', 'once'));
                is_g02 = ~isempty(regexp(line, 'G0*2', 'once'));
                is_g03 = ~isempty(regexp(line, 'G0*3', 'once'));
                
                % 提取坐标
                x_match = regexp(line, 'X([-\d.]+)', 'tokens');
                y_match = regexp(line, 'Y([-\d.]+)', 'tokens');
                
                % 提取圆弧参数
                r_match = regexp(line, 'R([-\d.]+)', 'tokens');
                i_match = regexp(line, 'I([-\d.]+)', 'tokens');
                j_match = regexp(line, 'J([-\d.]+)', 'tokens');
                
                if ~isempty(x_match) && ~isempty(y_match)
                    x = str2double(x_match{1}{1});
                    y = str2double(y_match{1}{1});
                    
                    % 创建新的路径段 - 预先定义所有可能的字段以确保结构体一致性
                    new_segment = struct('type', 0, ...
                                         'start_point', current_point, ...
                                         'end_point', [x, y], ...
                                         'direction', 0, ...
                                         'radius', 0, ...
                                         'center', [0, 0], ...
                                         'start_angle', 0, ...
                                         'end_angle', 0, ...
                                         'sweep_angle', 0);
                    
                    if is_g01 % 直线段
                        new_segment.type = 1; % 直线类型
                        
                    elseif is_g02 || is_g03 % 圆弧段
                        new_segment.type = 2; % 圆弧类型
                        new_segment.direction = is_g02 + 2 * is_g03; % 1表示顺时针(G02)，2表示逆时针(G03)
                        
                        % 确定圆心
                        if ~isempty(r_match) % 使用半径R格式
                            r = str2double(r_match{1}{1});
                            new_segment.radius = abs(r);
                            
                            % 计算圆心
                            [center_x, center_y] = calculateArcCenter(current_point(1), current_point(2), x, y, r, new_segment.direction);
                            new_segment.center = [center_x, center_y];
                            
                        elseif ~isempty(i_match) && ~isempty(j_match) % 使用I,J格式
                            i_val = str2double(i_match{1}{1});
                            j_val = str2double(j_match{1}{1});
                            
                            % 圆心是相对于起点的偏移
                            center_x = current_point(1) + i_val;
                            center_y = current_point(2) + j_val;
                            new_segment.center = [center_x, center_y];
                            
                            % 计算半径
                            new_segment.radius = sqrt((center_x - current_point(1))^2 + (center_y - current_point(2))^2);
                        end
                        
                        % 计算起点和终点相对于圆心的角度
                        start_angle = atan2(current_point(2) - new_segment.center(2), current_point(1) - new_segment.center(1));
                        end_angle = atan2(y - new_segment.center(2), x - new_segment.center(1));
                        
                        % 确保角度在正确的范围内
                        if new_segment.direction == 1 % 顺时针
                            if end_angle > start_angle
                                end_angle = end_angle - 2*pi;
                            end
                        else % 逆时针
                            if end_angle < start_angle
                                end_angle = end_angle + 2*pi;
                            end
                        end
                        
                        new_segment.start_angle = start_angle;
                        new_segment.end_angle = end_angle;
                        new_segment.sweep_angle = abs(end_angle - start_angle);
                    end
                    
                    % 添加新段到数组
                    if isempty(segments)
                        segments = new_segment;
                    else
                        segments(end+1) = new_segment;
                    end
                    
                    segment_types = [segment_types; new_segment.type];
                    
                    % 更新当前点
                    current_point = [x, y];
                end
            end

            % 关闭文件
            fclose(fileID);

            % 检查是否成功读取路径段
            if isempty(segments)
                disp('文件中没有有效的路径段数据');
                if display_mode == 2 || display_mode == 3 % 如果只显示G代码或光顺路径，则退出
                    return;
                else
                    % 如果是显示所有路径，则改为只显示机床路径
                    display_mode = 1;
                    disp('将只显示机床实际路径');
                end
            else
                disp('G代码文件读取成功');
                disp(['提取的路径段数量: ', num2str(length(segments))]);
                disp(['直线段数量: ', num2str(sum(segment_types == 1))]);
                disp(['圆弧段数量: ', num2str(sum(segment_types == 2))]);
            end
        end
    end
else
    % 如果不需要显示G代码路径，设置为空
    segments = [];
    filename_gcode = 'no_gcode_file';
end

% 如果没有成功读取任何数据，则退出
if (display_mode == 1 && isempty(x_machine)) || ...
   (display_mode >= 2 && isempty(segments))
    disp('没有足够的数据可以显示，程序退出');
    return;
end

%% 第四步：找出所有连接点并确定光顺类型
% 只有在需要显示光顺路径时才进行光顺处理
if display_mode == 2 || display_mode == 3 || display_mode == 4
    if ~isempty(segments)
        connection_points = [];
        connection_types = []; % 1=直线-直线, 2=直线-圆弧, 3=圆弧-直线, 4=圆弧-圆弧

        for i = 1:length(segments)-1
            % 这是一个连接点
            connection_point = [i, segments(i).end_point];
            
            % 确定连接类型
            if segments(i).type == 1 && segments(i+1).type == 1
                conn_type = 1; % 直线-直线
            elseif segments(i).type == 1 && segments(i+1).type == 2
                conn_type = 2; % 直线-圆弧
            elseif segments(i).type == 2 && segments(i+1).type == 1
                conn_type = 3; % 圆弧-直线
            else % segments(i).type == 2 && segments(i+1).type == 2
                conn_type = 4; % 圆弧-圆弧
            end
            
            % 如果是第一个连接点，直接赋值
            if isempty(connection_points)
                connection_points = connection_point;
                connection_types = conn_type;
            else
                % 否则添加到现有数组
                connection_points(end+1,:) = connection_point;
                connection_types(end+1) = conn_type;
            end
        end

        disp(['找到的连接点数量: ', num2str(size(connection_points, 1))]);
        disp('连接点类型统计:');
        disp(['直线-直线连接: ', num2str(sum(connection_types == 1))]);
        disp(['直线-圆弧连接: ', num2str(sum(connection_types == 2))]);
        disp(['圆弧-直线连接: ', num2str(sum(connection_types == 3))]);
        disp(['圆弧-圆弧连接: ', num2str(sum(connection_types == 4))]);

        %% 第五步：设置光顺参数
        disp(' ');
        pe = input('请输入路径光顺的最大逼近误差 (默认值 0.03)：');
        if isempty(pe)  % 如果用户直接按回车，使用默认值
            pe = 0.03;
        end

        %% 第六步：对每个连接点进行光顺处理
        % 初始化存储每个拐角光顺段的点
        all_smooth_points = cell(size(connection_points, 1), 1); % 使用cell数组存储每个拐角的光顺点

        % 定义B样条参数 - 确保G³连续性
        k = 5; % B样条次数为5，确保G³连续性
        % 使用均匀节点矢量，确保在连接点处具有足够的连续性
        NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1]; % 5次B样条需要6个重复节点

        % 处理每个连接点
        for i = 1:size(connection_points, 1)
            segment_idx = connection_points(i, 1);
            conn_type = connection_types(i);
            
            % 根据连接类型选择不同的光顺算法
            switch conn_type
                case 1 % 直线-直线
                    % 获取两个直线段
                    line_segment1 = segments(segment_idx);
                    line_segment2 = segments(segment_idx + 1);
                    
                    % 执行直线-直线光顺
                    [ctrls_pos, D] = lineLineSmoothing(pe, line_segment1, line_segment2);
                    
                case 2 % 直线-圆弧
                    % 获取直线段和圆弧段
                    line_segment = segments(segment_idx);
                    arc_segment = segments(segment_idx + 1);
                    
                    % 执行直线-圆弧对称光顺
                    [ctrls_pos, optimal_lp, optimal_theta_e] = lineArcSymmetricSmoothing(pe, line_segment, arc_segment);
                    
                case 3 % 圆弧-直线
                    % 获取圆弧段和直线段
                    arc_segment = segments(segment_idx);
                    line_segment = segments(segment_idx + 1);
                    
                    % 执行圆弧-直线对称光顺
                    [ctrls_pos, optimal_theta_e, optimal_lp] = arcLineSymmetricSmoothing(pe, arc_segment, line_segment);
                    
                case 4 % 圆弧-圆弧
                    % 获取两个圆弧段
                    arc_segment1 = segments(segment_idx);
                    arc_segment2 = segments(segment_idx + 1);
                    
                    % 执行圆弧-圆弧对称光顺
                    [ctrls_pos, optimal_theta_s, optimal_theta_e] = arcArcSymmetricSmoothing(pe, arc_segment1, arc_segment2);
            end
            
            % 计算光顺曲线上的点
            smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector);
            
            % 存储每个拐角的光顺点到cell数组
            all_smooth_points{i} = smooth_points;
        end

        disp('光顺处理完成，准备生成原始G代码路径的采样点...');
    else
        % 如果没有段数据，则不能进行光顺处理
        disp('无G代码段数据，无法进行光顺处理');
        all_smooth_points = {};
        connection_points = [];
        pe = 0.03; % 默认值
    end
else
    % 如果不需要显示光顺路径，则不进行光顺处理
    all_smooth_points = {};
    connection_points = [];
    pe = 0.03; % 默认值
end

%% 第七步：生成原始G代码路径的采样点
% 只有在需要显示G代码路径时才生成采样点
if display_mode == 2 || display_mode == 4
    if ~isempty(segments)
        % 初始化存储原始G代码路径的采样点
        gcode_path_x = [];
        gcode_path_y = [];

        % 对每个段进行采样
        for i = 1:length(segments)
            current_segment = segments(i);
            
            if current_segment.type == 1 % 直线段
                % 直线段只需要起点和终点
                start_point = current_segment.start_point;
                end_point = current_segment.end_point;
                
                % 添加到路径
                gcode_path_x = [gcode_path_x, start_point(1), end_point(1)];
                gcode_path_y = [gcode_path_y, start_point(2), end_point(2)];
                
            else % 圆弧段
                % 圆弧需要更多的采样点
                center = current_segment.center;
                radius = current_segment.radius;
                start_angle = current_segment.start_angle;
                end_angle = current_segment.end_angle;
                
                % 注意：角度已经在G代码解析时调整过了，这里直接使用存储的角度
                % 使用100个点来采样圆弧，与NewCompleteSmoothPath保持一致
                angles = linspace(start_angle, end_angle, 100);
                
                % 计算圆弧上的点
                arc_x = center(1) + radius * cos(angles);
                arc_y = center(2) + radius * sin(angles);
                
                % 添加到路径
                gcode_path_x = [gcode_path_x, arc_x];
                gcode_path_y = [gcode_path_y, arc_y];
            end
        end

        disp('路径采样完成，准备绘制对比图...');
    else
        % 如果没有段数据，则不能生成G代码路径
        gcode_path_x = [];
        gcode_path_y = [];
    end
else
    % 如果不需要显示G代码路径，则不生成采样点
    gcode_path_x = [];
    gcode_path_y = [];
end

%% 第八步：计算每个拐角到各路径的最小距离
% 只有在需要显示机床路径和光顺路径时才计算最小距离
if ((display_mode == 1 || display_mode == 4) && ~isempty(x_machine) && ...
   (display_mode == 2 || display_mode == 3 || display_mode == 4) && ~isempty(all_smooth_points)) || ...
   ((display_mode == 2 || display_mode == 3) && ~isempty(all_smooth_points))
    
    % 获取所有拐角点坐标
    if ~isempty(connection_points)
        corner_points = zeros(2, size(connection_points, 1));
        for i = 1:size(connection_points, 1)
            corner_points(:, i) = connection_points(i, 2:3)';
        end
        num_corners = size(corner_points, 2);

        % 初始化存储最小距离的数组
        min_machine_distances = zeros(1, num_corners);
        min_smooth_distances = zeros(1, num_corners);

        % 对每个拐角计算最小距离
        for i = 1:num_corners
            corner_point = corner_points(:, i);
            
            % 计算机床实际路径上每个点到当前拐点的距离
            if ~isempty(x_machine) && (display_mode == 1 || display_mode == 4)
                machine_distances = sqrt((x_machine - corner_point(1)).^2 + (y_machine - corner_point(2)).^2);
                min_machine_distances(i) = min(machine_distances);
            else
                min_machine_distances(i) = Inf;
            end
            
            % 计算光顺路径上每个点到当前拐点的距离 - 只需要考虑当前拐角的光顺点
            smooth_points = all_smooth_points{i};
            if ~isempty(smooth_points)
                smooth_distances = sqrt((smooth_points(1,:) - corner_point(1)).^2 + (smooth_points(2,:) - corner_point(2)).^2);
                min_smooth_distances(i) = min(smooth_distances);
            else
                min_smooth_distances(i) = Inf; % 如果没有光顺点，设置为无穷大
            end
        end

        % 显示最小距离
        disp('每个拐角的最小距离:');
        for i = 1:num_corners
            disp(['拐角 ', num2str(i), ' (', num2str(corner_points(1,i)), ',', num2str(corner_points(2,i)), '):']);
            if min_machine_distances(i) < Inf && (display_mode == 1 || display_mode == 4)
                disp(['  机床实际路径最小距离: ', num2str(min_machine_distances(i))]);
            end
            if min_smooth_distances(i) < Inf
                disp(['  系统光顺路径最小距离: ', num2str(min_smooth_distances(i))]);
            end
        end

        % 计算最大逼近误差（两条路径之间的最大差异）
        if display_mode == 4
            max_error_machine_smooth = max(abs(min_machine_distances - min_smooth_distances));
            disp(['机床路径与光顺路径之间的最大逼近误差: ', num2str(max_error_machine_smooth)]);
        end
    else
        corner_points = [];
        num_corners = 0;
        min_machine_distances = [];
        min_smooth_distances = [];
        max_error_machine_smooth = 0;
    end
else
    % 如果不需要同时显示机床路径和光顺路径，则不计算最小距离
    corner_points = [];
    if ~isempty(connection_points)
        corner_points = zeros(2, size(connection_points, 1));
        for i = 1:size(connection_points, 1)
            corner_points(:, i) = connection_points(i, 2:3)';
        end
    end
    num_corners = size(corner_points, 2);
    min_machine_distances = [];
    min_smooth_distances = [];
    max_error_machine_smooth = 0;
end

%% 第九步：创建图形窗口，包含路径对比图和柱状图
% 创建图形窗口
figure('Name', '路径对比与最小距离分析', 'NumberTitle', 'off', 'Position', [100, 100, 1200, 800]);

% 创建子图布局
subplot(1, 2, 1);
hold on;

% 根据选择的显示模式绘制路径
path_legend = {};
title_text = '';

% 绘制机床实际路径
if (display_mode == 1 || display_mode == 4) && ~isempty(x_machine)
    plot(x_machine, y_machine, 'r-', 'LineWidth', 1.5);
    path_legend = [path_legend, {'机床实际路径'}];
    title_text = ['机床路径: ', filename_machine];
end

% 绘制G代码路径
if (display_mode == 2 || display_mode == 4) && ~isempty(gcode_path_x)
    plot(gcode_path_x, gcode_path_y, 'b--', 'LineWidth', 1);
    path_legend = [path_legend, {'原始G代码路径'}];
    if isempty(title_text)
        title_text = ['G代码路径: ', filename_gcode];
    else
        title_text = [title_text, ' vs ', filename_gcode];
    end
end

% 绘制光顺路径
if (display_mode == 2 || display_mode == 3 || display_mode == 4) && ~isempty(all_smooth_points)
    for i = 1:length(all_smooth_points)
        smooth_points = all_smooth_points{i};
        if ~isempty(smooth_points)
            plot(smooth_points(1,:), smooth_points(2,:), 'g-', 'LineWidth', 1.5);
        end
    end
    path_legend = [path_legend, {'系统光顺路径'}];
    if isempty(title_text)
        title_text = '光顺路径';
    elseif strcmp(title_text, ['G代码路径: ', filename_gcode])
        title_text = ['G代码路径和光顺路径: ', filename_gcode];
    end
end

% 标记所有拐点
if ~isempty(corner_points) && display_mode ~= 2 && display_mode ~= 3
    for i = 1:size(corner_points, 2)
        plot(corner_points(1,i), corner_points(2,i), 'ko', 'MarkerSize', 8, 'MarkerFaceColor', 'y');
        text(corner_points(1,i)+0.1, corner_points(2,i)+0.1, ['拐点', num2str(i)], 'FontSize', 10);
    end
    path_legend = [path_legend, {'拐点'}];
end

% 标记起点和终点
if ~isempty(segments)
    start_point = segments(1).start_point;
    end_point = segments(end).end_point;
    plot(start_point(1), start_point(2), 'ko', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
    plot(end_point(1), end_point(2), 'ko', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
    path_legend = [path_legend, {'起点/终点'}];
end

% 添加图例
if ~isempty(path_legend)
    legend(path_legend, 'Location', 'best');
end

% 设置坐标轴等比例
axis equal;
grid on;
title(title_text, 'FontSize', 14);
xlabel('X 坐标', 'FontSize', 12);
ylabel('Y 坐标', 'FontSize', 12);

% 计算总光顺点数
total_smooth_points = 0;
for i = 1:length(all_smooth_points)
    if ~isempty(all_smooth_points{i})
        total_smooth_points = total_smooth_points + size(all_smooth_points{i}, 2);
    end
end

% 添加信息框
if display_mode == 4
    info_text = sprintf('最大逼近误差: %.3f\n机床路径点数: %d\n光顺路径点数: %d\n最大逼近误差: %.3f', ...
        pe, length(x_machine), total_smooth_points, max_error_machine_smooth);
elseif display_mode == 1 && ~isempty(x_machine)
    info_text = sprintf('机床路径点数: %d', length(x_machine));
elseif display_mode == 2 && ~isempty(gcode_path_x)
    info_text = sprintf('G代码路径点数: %d\n光顺路径点数: %d\n光顺误差: %.3f', length(gcode_path_x), total_smooth_points, pe);
elseif display_mode == 3 && total_smooth_points > 0
    info_text = sprintf('光顺路径点数: %d\n光顺误差: %.3f', total_smooth_points, pe);
else
    info_text = '';
end

if ~isempty(info_text)
    annotation('textbox', [0.05, 0.02, 0.3, 0.1], 'String', info_text, ...
        'EdgeColor', 'none', 'HorizontalAlignment', 'left', 'FontSize', 10);
end

% 创建柱状图子图
subplot(1, 2, 2);

% 准备数据
if num_corners > 0
    % 根据显示模式调整柱状图
    switch display_mode
        case 1 % 仅显示机床实际路径
            if ~isempty(min_machine_distances)
                % 创建X轴位置
                x_pos = 1:num_corners;
                % 绘制柱状图
                bar(x_pos, min_machine_distances, 'r');
                % 添加标题和标签
                title('各拐角点的机床路径最小距离', 'FontSize', 20);
                % 在每个拐角上方添加数值标签
                for i = 1:num_corners
                    if min_machine_distances(i) < Inf
                        text(x_pos(i), min_machine_distances(i)+0.01, sprintf('%.3f', min_machine_distances(i)), ...
                            'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize',15);
                    end
                end
                
                % 设置坐标轴和标签
                xlabel('拐角点', 'FontSize', 20);
                ylabel('最小距离', 'FontSize', 20);
                
                % 设置X轴刻度位置和标签
                set(gca, 'XTick', x_pos);
                corner_labels = cell(1, num_corners);
                for i = 1:num_corners
                    corner_labels{i} = ['拐角', num2str(i)];
                end
                set(gca, 'XTickLabel', corner_labels);
                xlim([0.5, num_corners+0.5]);  % 设置X轴范围，确保显示完整
                grid on;
            else
                text(0.5, 0.5, '没有足够的数据生成柱状图', ...
                    'HorizontalAlignment', 'center', 'FontSize', 15);
                axis off;
            end
            
        case 2 % 显示G代码路径和光顺路径
            if ~isempty(min_smooth_distances)
                % 创建X轴位置
                x_pos = 1:num_corners;
                % 绘制柱状图
                bar(x_pos, min_smooth_distances, 'g');
                % 添加标题和标签
                title('各拐角点的光顺路径最小距离', 'FontSize', 20);
                % 在每个拐角上方添加数值标签
                for i = 1:num_corners
                    if min_smooth_distances(i) < Inf
                        text(x_pos(i), min_smooth_distances(i)+0.01, sprintf('%.3f', min_smooth_distances(i)), ...
                            'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize',15);
                    end
                end
                
                % 设置坐标轴和标签
                xlabel('拐角点', 'FontSize', 20);
                ylabel('最小距离', 'FontSize', 20);
                
                % 设置X轴刻度位置和标签
                set(gca, 'XTick', x_pos);
                corner_labels = cell(1, num_corners);
                for i = 1:num_corners
                    corner_labels{i} = ['拐角', num2str(i)];
                end
                set(gca, 'XTickLabel', corner_labels);
                xlim([0.5, num_corners+0.5]);  % 设置X轴范围，确保显示完整
                grid on;
            else
                text(0.5, 0.5, '没有足够的数据生成柱状图', ...
                     'HorizontalAlignment', 'center', 'FontSize', 15);
                axis off;
            end
            
        case 3 % 仅显示光顺路径
            if ~isempty(min_smooth_distances)
                % 创建X轴位置
                x_pos = 1:num_corners;
                % 绘制柱状图
                bar(x_pos, min_smooth_distances, 'g');
                % 添加标题和标签
                title('各拐角点的光顺路径最小距离', 'FontSize', 20);
                % 在每个拐角上方添加数值标签
                for i = 1:num_corners
                    if min_smooth_distances(i) < Inf
                        text(x_pos(i), min_smooth_distances(i)+0.01, sprintf('%.3f', min_smooth_distances(i)), ...
                            'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize',15);
                    end
                end
                
                % 设置坐标轴和标签
                xlabel('拐角点', 'FontSize', 20);
                ylabel('最小距离', 'FontSize', 20);
                
                % 设置X轴刻度位置和标签
                set(gca, 'XTick', x_pos);
                corner_labels = cell(1, num_corners);
                for i = 1:num_corners
                    corner_labels{i} = ['拐角', num2str(i)];
                end
                set(gca, 'XTickLabel', corner_labels);
                xlim([0.5, num_corners+0.5]);  % 设置X轴范围，确保显示完整
                grid on;
            else
                text(0.5, 0.5, '没有足够的数据生成柱状图', ...
                    'HorizontalAlignment', 'center', 'FontSize', 15);
                axis off;
            end
            
        case 4 % 显示所有路径
            if ~isempty(min_machine_distances) && ~isempty(min_smooth_distances)
                % 创建X轴位置
                x_pos = 1:num_corners;
                bar_width = 0.8 / 2;  % 每个柱形的宽度
                
                % 创建分组柱状图 - 使用手动方式绘制以控制间距
                hold on;
                for i = 1:num_corners
                    % 机床实际路径柱形 - 红色
                    if min_machine_distances(i) < Inf
                        bar(x_pos(i)-bar_width/2, min_machine_distances(i), bar_width, 'FaceColor', 'r');
                        % 机床实际路径数值
                        text(x_pos(i)-bar_width/2, min_machine_distances(i)+0.01, sprintf('%.3f', min_machine_distances(i)), ...
                            'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize',15);
                    end
                    
                    % 系统光顺路径柱形 - 绿色
                    if min_smooth_distances(i) < Inf
                        bar(x_pos(i)+bar_width/2, min_smooth_distances(i), bar_width, 'FaceColor', 'g');
                        % 系统光顺路径数值
                        text(x_pos(i)+bar_width/2, min_smooth_distances(i)+0.01, sprintf('%.3f', min_smooth_distances(i)), ...
                            'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize',15);
                    end
                end
                hold off;
                
                % 添加标题和标签
                title('各拐角点的最小距离对比', 'FontSize', 20);
                
                % 添加图例
                legend({'机床实际路径', '系统光顺路径'}, 'Location', 'best');
                
                % 设置坐标轴和标签
                xlabel('拐角点', 'FontSize', 20);
                ylabel('最小距离', 'FontSize', 20);
                
                % 设置X轴刻度位置和标签
                set(gca, 'XTick', x_pos);
                corner_labels = cell(1, num_corners);
                for i = 1:num_corners
                    corner_labels{i} = ['拐角', num2str(i)];
                end
                set(gca, 'XTickLabel', corner_labels);
                xlim([0.5, num_corners+0.5]);  % 设置X轴范围，确保显示完整
                grid on;
            else
                text(0.5, 0.5, '没有足够的数据生成柱状图', ...
                    'HorizontalAlignment', 'center', 'FontSize', 15);
                axis off;
            end
    end
else
    % 没有拐角点，无法生成柱状图
    text(0.5, 0.5, '没有拐角点数据，无法生成柱状图', ...
         'HorizontalAlignment', 'center', 'FontSize', 15);
    axis off;
end

% 保存图像
output_filename = ['路径对比_'];
if ~strcmp(filename_machine, 'no_machine_file')
    output_filename = [output_filename, strrep(filename_machine, '.txt', '')];
end
if ~strcmp(filename_gcode, 'no_gcode_file')
    if ~strcmp(filename_machine, 'no_machine_file')
        output_filename = [output_filename, '_vs_'];
    end
    output_filename = [output_filename, strrep(filename_gcode, '.txt', '')];
end
output_filename = [output_filename, '_mode', num2str(display_mode), '.png'];

disp('程序执行完成！');
end

%% 圆弧中心计算函数
function [center_x, center_y] = calculateArcCenter(x1, y1, x2, y2, r, direction)
% 根据起点、终点、半径和方向计算圆弧中心点
% 输入:
%   x1, y1: 起点坐标
%   x2, y2: 终点坐标
%   r: 半径
%   direction: 1表示顺时针(G02)，2表示逆时针(G03)
% 输出:
%   center_x, center_y: 圆心坐标

% 计算起点和终点之间的中点
mid_x = (x1 + x2) / 2;
mid_y = (y1 + y2) / 2;

% 计算起点和终点之间的距离
chord = sqrt((x2 - x1)^2 + (y2 - y1)^2);

% 检查半径是否足够大
if abs(r) < chord/2
    error('半径太小，无法创建圆弧');
end

% 计算从中点到圆心的距离
h = sqrt(r^2 - (chord/2)^2);

% 计算起点到终点的向量
dx = x2 - x1;
dy = y2 - y1;

% 单位化
len = sqrt(dx^2 + dy^2);
dx = dx / len;
dy = dy / len;

% 计算垂直于起点到终点向量的单位向量
% 对于G02(顺时针)和G03(逆时针)，垂直方向相反
if direction == 1 % 顺时针(G02)
    nx = dy;   % 向右旋转90度
    ny = -dx;
else % 逆时针(G03)
    nx = -dy;  % 向左旋转90度
    ny = dx;
end

% 计算圆心
center_x = mid_x + h * nx;
center_y = mid_y + h * ny;
end

%% 直线-直线光顺函数
function [ctrls_pos, D] = lineLineSmoothing(pe, line_segment1, line_segment2)
% 计算直线-直线连接处的光顺控制点
% 输入:
%   pe: 最大逼近误差
%   line_segment1: 第一条直线段
%   line_segment2: 第二条直线段
% 输出:
%   ctrls_pos: 7个控制点的坐标 [2x7]
%   D: 光顺参数D

% 获取拐角点和相邻点
p1 = line_segment1.start_point';
p2 = line_segment1.end_point'; % 拐角点
p3 = line_segment2.end_point';

% 计算方向向量
v1 = p1 - p2; % 第一条直线的方向向量
v2 = p3 - p2; % 第二条直线的方向向量

% 计算直线长度
L1 = norm(v1);
L2 = norm(v2);

% 计算单位向量
m1 = v1 / L1;
m2 = v2 / L2;

% 计算拐角角度
cos_theta = dot(m1, m2);
cos_theta = max(min(cos_theta, 1), -1); % 确保在[-1, 1]范围内
theta = acos(cos_theta);

% 计算光顺参数lp (根据误差要求和角度)
lim = 4 * pe / (3 * cos(0.5 * theta));
lp = min(lim, 0.2 * min(L1, L2));
D = 2.5 * lp;

% 计算7个控制点
ctrls_pos = zeros(2, 7);
ctrls_pos(:, 4) = p2; % 中心控制点是拐角点

% 计算其他控制点
ctrls_pos(:, 3) = p2 + m1 * lp;
ctrls_pos(:, 5) = p2 + m2 * lp;
ctrls_pos(:, 2) = ctrls_pos(:, 3) * 2 - p2;
ctrls_pos(:, 1) = 0.5 * (ctrls_pos(:, 3) * 5 - p2 * 3);
ctrls_pos(:, 6) = ctrls_pos(:, 5) * 2 - p2;
ctrls_pos(:, 7) = 0.5 * (ctrls_pos(:, 5) * 5 - p2 * 3);
end

%% 直线-圆弧光顺函数 (最终版：带边界决策的对称优化法)
function [ctrls_pos, optimal_lp, optimal_theta_e] = lineArcSymmetricSmoothing(pe, line_segment, arc_segment)
    % --- 几何信息获取 ---
    p_i = line_segment.end_point(:);
    v_line = p_i - line_segment.start_point(:);
    m_line = v_line / norm(v_line);
    center = arc_segment.center(:);
    v_arc = p_i - center;
    r = arc_segment.radius;
    if arc_segment.direction == 2
        m_arc = [-v_arc(2); v_arc(1)] / r;
    else
        m_arc = [v_arc(2); -v_arc(1)] / r;
    end
    m_in = m_line;
    m_out = m_arc;

    % --- G1连续性检查 ---
    if norm(m_in - m_out) < 1e-6
        disp(['在点 (', num2str(p_i(1)), ', ', num2str(p_i(2)), ') 检测到G1连续，跳过光顺。']);
        ctrls_pos = repmat(p_i, 1, 7); 
        optimal_lp = 0;
        optimal_theta_e = 0;
        return; 
    end

    % ======================= 最终算法逻辑开始 =======================

    % --- 步骤 1：计算几何允许的最大对称光顺角 theta_max_geom ---
    % 计算直线段长度和圆弧段扫描角度
    line_length = norm(line_segment.end_point - line_segment.start_point);
    arc_sweep_angle = arc_segment.sweep_angle;

    % 1a. 来自直线侧的约束（转换为角度theta_e）
    lp_max_from_line = 0.5 * line_length;
    % 将直线约束转换为角度约束
    asin_arg = lp_max_from_line / (2 * r);
    if asin_arg > 1
        theta_max_from_line = pi; % 直线很长，此约束无效
    else
        theta_max_from_line = 2 * asin(asin_arg);
    end

    % 1b. 来自圆弧侧的约束
    theta_max_from_arc = 0.5 * arc_sweep_angle;

    % 1c. 几何允许的最大光顺角是所有约束中的最小值
    theta_max_geom = min([pi/2, theta_max_from_line, theta_max_from_arc]);
    theta_max_geom = max(theta_max_geom, 0.001); % 保证不为零

    % --- 步骤 2：计算满足精度pe的理想光顺角 theta_for_pe ---
    % 计算几何约束点 P_constraint
    v_diff = m_out - m_in;
    v_bisector = v_diff / norm(v_diff);
    P_constraint = p_i + pe * v_bisector;

    % 定义单变量目标函数 (优化变量是theta_e)
    objectiveHandle = @(theta_e) objective_symmetric_line_arc(theta_e, P_constraint, p_i, line_segment, arc_segment);
    
    % 调用fminbnd求解器，寻找理想解
    options = optimset('TolX', 1e-8, 'Display', 'off');
    % 搜索范围可以比较宽松，因为最终结果会被theta_max_geom约束
    theta_for_pe = fminbnd(objectiveHandle, 0.001, min(pi/2, arc_sweep_angle), options);

    % --- 步骤 3：最终决策 ---
    if theta_for_pe <= theta_max_geom
        % 理想解未超限，采纳理想解，精度得到保证
        disp(['决策: 采用理想精度解, 误差 ≈ ', num2str(pe)]);
        optimal_theta_e = theta_for_pe;
    else
        % 理想解已超限，采纳几何极限解，保证不超限，但牺牲部分精度
        disp(['决策: 理想解超限，采用几何极限解以保证不干涉。误差将小于 ', num2str(pe)]);
        optimal_theta_e = theta_max_geom;
    end

    % 根据最终确定的最优theta_e，计算对应的lp
    optimal_lp = 2 * r * sin(optimal_theta_e / 2);

    % 使用最优参数计算控制点
    ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, optimal_theta_e, optimal_lp, true, arc_segment);
end

function cost = objective_symmetric_line_arc(theta_e, P_constraint, p_i, line_segment, arc_segment)
    r = arc_segment.radius;
    lp = 2 * r * sin(theta_e / 2);
    v_line = p_i - line_segment.start_point(:);
    m_line = v_line / norm(v_line);
    ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, theta_e, lp, true, arc_segment);
    k = 5;
    NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
    mid_point = computeBsplinePoint(ctrls_pos, 0.5, k, NodeVector);
    error_vector = mid_point - P_constraint;
    cost = sum(error_vector.^2);
end

%% 圆弧-直线光顺函数 (基于优化的直接调整法)
function [ctrls_pos, optimal_theta_e, optimal_lp] = arcLineSymmetricSmoothing(pe, arc_segment, line_segment)
    % --- 几何信息获取 ---
    p_i = arc_segment.end_point(:);
    v_line = line_segment.end_point(:) - p_i;
    m_line = v_line / norm(v_line);
    center = arc_segment.center(:);
    v_arc = p_i - center;
    r = arc_segment.radius;
    if arc_segment.direction == 2
        m_arc = [-v_arc(2); v_arc(1)] / r;
    else
        m_arc = [v_arc(2); -v_arc(1)] / r;
    end
    m_in = m_arc;
    m_out = m_line;

    % --- G1连续性检查 ---
    if norm(m_in - m_out) < 1e-6
        disp(['在点 (', num2str(p_i(1)), ', ', num2str(p_i(2)), ') 检测到G1连续，跳过光顺。']);
        ctrls_pos = repmat(p_i, 1, 7); 
        optimal_theta_e = 0;
        optimal_lp = 0;
        return; 
    end

    % ======================= 最终算法逻辑开始 =======================

    % --- 步骤 1：计算几何允许的最大对称光顺角 theta_max_geom ---
    arc_sweep_angle = arc_segment.sweep_angle;
    line_length = norm(line_segment.end_point(:) - p_i);

    % 1a. 来自圆弧侧的约束
    theta_max_from_arc = 0.5 * arc_sweep_angle;

    % 1b. 来自直线侧的约束（通过对称关系换算）
    asin_arg = (0.5 * line_length) / (2 * r);
    if asin_arg > 1
        theta_max_from_line = pi; % 直线很长，此约束无效
    else
        theta_max_from_line = 2 * asin(asin_arg);
    end

    % 1c. 几何允许的最大光顺角是所有约束中的最小值
    theta_max_geom = min([pi/2, theta_max_from_arc, theta_max_from_line]);
    theta_max_geom = max(theta_max_geom, 0.001); % 保证不为零

    % --- 步骤 2：计算满足精度pe的理想光顺角 theta_for_pe ---
    % 计算几何约束点 P_constraint
    v_diff = m_out - m_in;
    v_bisector = v_diff / norm(v_diff);
    P_constraint = p_i + pe * v_bisector;

    % 定义单变量目标函数 (优化变量是theta_e)
    objectiveHandle = @(theta_e) objective_symmetric_arc_line(theta_e, P_constraint, p_i, arc_segment, line_segment);
    
    % 调用fminbnd求解器，寻找理想解
    options = optimset('TolX', 1e-8, 'Display', 'off');
    % 搜索范围可以比较宽松，因为最终结果会被theta_max_geom约束
    theta_for_pe = fminbnd(objectiveHandle, 0.001, min(pi/2, arc_sweep_angle), options);

    % --- 步骤 3：最终决策 ---
    if theta_for_pe <= theta_max_geom
        % 理想解未超限，采纳理想解，精度得到保证
        disp(['决策: 采用理想精度解, 误差 ≈ ', num2str(pe)]);
        optimal_theta_e = theta_for_pe;
    else
        % 理想解已超限，采纳几何极限解，保证不超限，但牺牲部分精度
        disp(['决策: 理想解超限，采用几何极限解以保证不干涉。误差将小于 ', num2str(pe)]);
        optimal_theta_e = theta_max_geom;
    end

    % 根据最终确定的最优theta_e，计算对应的lp
    optimal_lp = 2 * r * sin(optimal_theta_e / 2);

    % 使用最优参数计算控制点
    ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, optimal_theta_e, optimal_lp, false, arc_segment);
end

function cost = objective_symmetric_arc_line(theta_e, P_constraint, p_i, arc_segment, line_segment)
    lp = 2 * arc_segment.radius * sin(theta_e / 2);
    m_line = (line_segment.end_point(:) - p_i(:)) / norm(line_segment.end_point(:) - p_i(:));
    r = arc_segment.radius;
    ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, theta_e, lp, false, arc_segment);
    k = 5;
    NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
    mid_point = computeBsplinePoint(ctrls_pos, 0.5, k, NodeVector);
    error_vector = mid_point - P_constraint;
    cost = sum(error_vector.^2);
end

%% 圆弧-圆弧光顺函数 (最终版：带边界决策的对称优化法)
function [ctrls_pos, optimal_theta_s, optimal_theta_e] = arcArcSymmetricSmoothing(pe, arc_segment1, arc_segment2)
    % --- 几何信息获取 ---
    p_i = arc_segment1.end_point(:);
    center1 = arc_segment1.center(:);
    v_arc1 = p_i - center1;
    r1 = arc_segment1.radius;
    if arc_segment1.direction == 2
        m_arc1 = [-v_arc1(2); v_arc1(1)] / r1;
    else
        m_arc1 = [v_arc1(2); -v_arc1(1)] / r1;
    end
    
    center2 = arc_segment2.center(:);
    v_arc2 = p_i - center2;
    r2 = arc_segment2.radius;
    if arc_segment2.direction == 2
        m_arc2 = [-v_arc2(2); v_arc2(1)] / r2;
    else
        m_arc2 = [v_arc2(2); -v_arc2(1)] / r2;
    end
    
    m_in = m_arc1;
    m_out = m_arc2;
    
    % 计算半径比例
    r_ratio = r1 / r2;

    % --- G1连续性检查 ---
    if norm(m_in - m_out) < 1e-6
        disp(['在点 (', num2str(p_i(1)), ', ', num2str(p_i(2)), ') 检测到G1连续，跳过光顺。']);
        ctrls_pos = repmat(p_i, 1, 7); 
        optimal_theta_s = 0;
        optimal_theta_e = 0;
        return; 
    end

    % ======================= 最终算法逻辑开始 =======================

    % --- 步骤 1：计算几何允许的最大对称光顺角 theta_max_geom ---
    % 计算两条圆弧段扫描角度
    arc_sweep_angle1 = arc_segment1.sweep_angle;
    arc_sweep_angle2 = arc_segment2.sweep_angle;

    % 1a. 来自第一条圆弧侧的约束
    theta_s_max_from_arc1 = 0.5 * arc_sweep_angle1;

    % 1b. 来自第二条圆弧侧的约束（通过对称关系换算）
    % 第二圆弧的角度限制换算到第一圆弧上
    asin_arg = sin(0.25 * arc_sweep_angle2) / r_ratio;
    if abs(asin_arg) <= 1
        theta_s_max_from_arc2 = 2 * asin(asin_arg);
    else
        % 如果参数无效，说明第二圆弧不会构成约束
        theta_s_max_from_arc2 = pi/2;
    end

    % 1c. 几何允许的最大光顺角是所有约束中的最小值
    theta_s_max_geom = min([pi/2, theta_s_max_from_arc1, theta_s_max_from_arc2]);
    theta_s_max_geom = max(theta_s_max_geom, 0.001); % 保证不为零

    % --- 步骤 2：计算满足精度pe的理想光顺角 theta_for_pe ---
    % 计算几何约束点 P_constraint
    v_diff = m_out - m_in;
    v_bisector = v_diff / norm(v_diff);
    P_constraint = p_i + pe * v_bisector;

    % 定义单变量目标函数 (优化变量是theta_s)
    objectiveHandle = @(theta_s) objective_symmetric_arc_arc(theta_s, P_constraint, p_i, arc_segment1, arc_segment2, r_ratio);
    
    % 调用fminbnd求解器，寻找理想解
    options = optimset('TolX', 1e-8, 'Display', 'off');
    % 搜索范围可以比较宽松，因为最终结果会被theta_max_geom约束
    theta_s_for_pe = fminbnd(objectiveHandle, 0.001, min(pi/2, arc_sweep_angle1), options);

    % --- 步骤 3：最终决策 ---
    if theta_s_for_pe <= theta_s_max_geom
        % 理想解未超限，采纳理想解，精度得到保证
        disp(['决策: 采用理想精度解, 误差 ≈ ', num2str(pe)]);
        optimal_theta_s = theta_s_for_pe;
    else
        % 理想解已超限，采纳几何极限解，保证不超限，但牺牲部分精度
        disp(['决策: 理想解超限，采用几何极限解以保证不干涉。误差将小于 ', num2str(pe)]);
        optimal_theta_s = theta_s_max_geom;
    end

    % 根据最终确定的最优theta_s，计算对应的theta_e
    asin_arg = r_ratio * sin(optimal_theta_s / 2);
    if abs(asin_arg) > 1
        % 如果arcsin参数无效（不应该发生，因为我们已经检查了几何约束）
        disp('警告: arcsin参数无效，使用近似值。');
        asin_arg = sign(asin_arg) * 0.9999;
    end
    optimal_theta_e = 2 * asin(asin_arg);

    % 使用最优参数计算控制点
    ctrls_pos = calculateControlPoints_ArcArc_Correct(p_i, arc_segment1, arc_segment2, optimal_theta_s, optimal_theta_e);
end

function cost = objective_symmetric_arc_arc(theta_s, P_constraint, p_i, arc_segment1, arc_segment2, r_ratio)
    % 根据对称约束计算第二圆弧侧光顺角度
    asin_arg = r_ratio * sin(theta_s / 2);
    if abs(asin_arg) > 1
        % 如果参数无效，返回一个巨大的惩罚值
        cost = inf;
        return;
    end
    theta_e = 2 * asin(asin_arg);
    
    % 计算控制点
    ctrls_pos = calculateControlPoints_ArcArc_Correct(p_i, arc_segment1, arc_segment2, theta_s, theta_e);
    
    % 计算B样条曲线在u=0.5处的点
    k = 5;
    NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
    mid_point = computeBsplinePoint(ctrls_pos, 0.5, k, NodeVector);
    
    % 计算误差向量的平方和
    error_vector = mid_point - P_constraint;
    cost = sum(error_vector.^2);
end

%% 直线-圆弧控制点计算函数
function ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, theta_e, lp_s, line_to_arc, arc_segment)
% 计算直线-圆弧或圆弧-直线连接处的B样条控制点
% 输入:
%   p_i: 连接点坐标
%   m_line: 直线方向单位向量
%   r: 圆弧半径
%   theta_e: 光顺角度
%   lp_s: 直线侧光顺长度
%   line_to_arc: 布尔值，true表示直线-圆弧，false表示圆弧-直线
%   arc_segment: 圆弧段
% 输出:
%   ctrls_pos: 7个控制点的坐标 [2x7]

% 确保输入向量是列向量
p_i = p_i(:);
m_line = m_line(:);

% 初始化
ctrls_pos = zeros(2, 7);
d3 = p_i;
ctrls_pos(:, 4) = d3;
center = arc_segment.center(:); % 确保是列向量
arc_direction = arc_segment.direction;
c_l1 = 0.35; c_l2 = 0.45; c_l3 = 0.2;

if line_to_arc
    %% CASE 1: 直线 -> 圆弧
    % 1. 直线侧控制点 d0, d1, d2
    ctrls_pos(:, 3) = d3 - c_l1 * lp_s * m_line;
    ctrls_pos(:, 2) = ctrls_pos(:, 3) - c_l2 * lp_s * m_line;
    ctrls_pos(:, 1) = ctrls_pos(:, 2) - c_l3 * lp_s * m_line;

    % 2. 圆弧侧控制点 d4, d5, d6
    angle_at_d3 = atan2(p_i(2) - center(2), p_i(1) - center(1));
    if arc_direction == 2 % 逆时针 G03
        angle_at_d6 = angle_at_d3 + theta_e;
    else % 顺时针 G02
        angle_at_d6 = angle_at_d3 - theta_e;
    end
    d6 = center + r * [cos(angle_at_d6); sin(angle_at_d6)];
    
    if arc_direction == 2 % 逆时针 G03
        m_e = [-sin(angle_at_d6); cos(angle_at_d6)];
    else % 顺时针 G02
        m_e = [sin(angle_at_d6); -cos(angle_at_d6)];
    end

    vec_d6_c = d6 - center;
    d5 = d6 - (0.4 * r * sin(theta_e)) * m_e;
    d4 = (0.8 + 0.2*cos(theta_e))*vec_d6_c - (0.57*r*sin(theta_e))*m_e + center;
    
    ctrls_pos(:, 7) = d6;
    ctrls_pos(:, 6) = d5;
    ctrls_pos(:, 5) = d4;

else
    %% CASE 2: 圆弧 -> 直线
    % 1. 直线侧控制点 d4, d5, d6
    ctrls_pos(:, 5) = d3 + c_l1 * lp_s * m_line;
    ctrls_pos(:, 6) = ctrls_pos(:, 5) + c_l2 * lp_s * m_line;
    ctrls_pos(:, 7) = ctrls_pos(:, 6) + c_l3 * lp_s * m_line;

    % 2. 圆弧侧控制点 d0, d1, d2
    angle_at_d3 = atan2(p_i(2) - center(2), p_i(1) - center(1));
    if arc_direction == 2 % 逆时针 G03
        angle_at_d0 = angle_at_d3 - theta_e;
    else % 顺时针 G02
        angle_at_d0 = angle_at_d3 + theta_e;
    end
    d0 = center + r * [cos(angle_at_d0); sin(angle_at_d0)];
    
    if arc_direction == 2 % 逆时针 G03
        m_s = [-sin(angle_at_d0); cos(angle_at_d0)];
    else % 顺时针 G02
        m_s = [sin(angle_at_d0); -cos(angle_at_d0)];
    end

    vec_d0_c = d0 - center;
    d1 = d0 + (0.4 * r * sin(theta_e)) * m_s;
    d2 = (0.8 + 0.2*cos(theta_e))*vec_d0_c + (0.57*r*sin(theta_e))*m_s + center;
    
    ctrls_pos(:, 1) = d0;
    ctrls_pos(:, 2) = d1;
    ctrls_pos(:, 3) = d2;
end
end

%% 正确的圆弧-圆弧控制点计算函数
function ctrls_pos = calculateControlPoints_ArcArc_Correct(p_i, arc1, arc2, theta_s, theta_e)
% 根据第一篇论文的公式，计算圆弧-圆弧光顺的7个五次B样条控制点
% p_i: 角隅点坐标 (列向量)
% arc1, arc2: 两个圆弧段结构体
% theta_s, theta_e: 两侧的光顺角度

% 初始化控制点数组 (2x7)
ctrls_pos = zeros(2, 7);
% 中心控制点 d3 即为角隅点 p_i
ctrls_pos(:, 4) = p_i(:); % 确保是列向量

%% 计算第一段圆弧侧(s-side)的控制点 d0, d1, d2
center_s = arc1.center(:);
angle_at_d3_s = atan2(p_i(2) - center_s(2), p_i(1) - center_s(1));

% 根据圆弧方向回退角度，找到光顺段起点d0
if arc1.direction == 2 % 逆时针 G03
    angle_at_d0 = angle_at_d3_s - theta_s;
else % 顺时针 G02
    angle_at_d0 = angle_at_d3_s + theta_s;
end
d0 = center_s + arc1.radius * [cos(angle_at_d0); sin(angle_at_d0)];

% 计算 d0 处的切线向量 m_s
vec_d0_c = d0 - center_s;
if arc1.direction == 2 % 逆时针 G03
    m_s = [-vec_d0_c(2); vec_d0_c(1)] / norm(vec_d0_c);
else % 顺时针 G02
    m_s = [vec_d0_c(2); -vec_d0_c(1)] / norm(vec_d0_c);
end

% 根据论文公式(10)的原理计算 d1 和 d2
d1 = d0 + (0.4 * arc1.radius * sin(theta_s)) * m_s;
d2 = (0.8 + 0.2*cos(theta_s))*vec_d0_c + (0.57 * arc1.radius * sin(theta_s))*m_s + center_s;

ctrls_pos(:, 1) = d0;
ctrls_pos(:, 2) = d1;
ctrls_pos(:, 3) = d2;

%% 计算第二段圆弧侧(e-side)的控制点 d4, d5, d6
center_e = arc2.center(:);
angle_at_d3_e = atan2(p_i(2) - center_e(2), p_i(1) - center_e(1));

% 根据圆弧方向前进角度，找到光顺段终点d6
if arc2.direction == 2 % 逆时针 G03
    angle_at_d6 = angle_at_d3_e + theta_e;
else % 顺时针 G02
    angle_at_d6 = angle_at_d3_e - theta_e;
end
d6 = center_e + arc2.radius * [cos(angle_at_d6); sin(angle_at_d6)];

% 计算 d6 处的切线向量 m_e
vec_d6_c = d6 - center_e;
if arc2.direction == 2 % 逆时针 G03
    m_e = [-vec_d6_c(2); vec_d6_c(1)] / norm(vec_d6_c);
else % 顺时针 G02
    m_e = [vec_d6_c(2); -vec_d6_c(1)] / norm(vec_d6_c);
end

% 根据论文公式(9)的原理计算 d4 和 d5
d5 = d6 - (0.4 * arc2.radius * sin(theta_e)) * m_e;
d4 = (0.8 + 0.2*cos(theta_e))*vec_d6_c - (0.57 * arc2.radius * sin(theta_e))*m_e + center_e;

ctrls_pos(:, 7) = d6;
ctrls_pos(:, 6) = d5;
ctrls_pos(:, 5) = d4;
end

%% B样条曲线计算函数
function smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector)
% 计算B样条曲线上的一系列点
% 参数:
%   ctrls_pos: 控制点坐标，每列是一个控制点的[x;y]坐标
%   k: B样条次数
%   NodeVector: 节点矢量

% 设置采样点数量
num_samples = 100;
u_values = linspace(0, 1, num_samples);

% 初始化结果数组
smooth_points = zeros(2, num_samples);

% 计算每个参数值对应的点
for idx = 1:num_samples
    u = u_values(idx);
    smooth_points(:, idx) = computeBsplinePoint(ctrls_pos, u, k, NodeVector);
end
end

function p = computeBsplinePoint(ctrls_pos, u, k, NodeVector)
% 计算B样条上参数u对应的点
n = size(ctrls_pos, 2) - 1;
Nik = zeros(n+1, 1);

for i1 = 0:n
    Nik(i1+1, 1) = BaseFunction(i1+1, k, u, NodeVector);
end

p = ctrls_pos * Nik; % 计算样条上的点
end

function val = BaseFunction(i, k, u, NodeVector)
% B样条基函数
% i为控制点序号；k为B样条次数；u为样条参数；NodeVector为节点矢量
if k==0    
    if abs(u-NodeVector(end))<10^-6
        if NodeVector(i)<=u && abs(u-NodeVector(i+1))<10^-6
            val=1;return;
        else
            val=0;return;
        end
    else
        if(u<NodeVector(i)||u>=NodeVector(i+1))
            val=0;return;
        else
            val=1;return;
        end
    end
end

if k>0
    if(u<NodeVector(i)||u>NodeVector(i+k+1)) 
        val=0;
    else
        dtemp=NodeVector(i+k)-NodeVector(i);
        if(dtemp==0) 
            alpha=0;
        else
            alpha=(u-NodeVector(i))/dtemp;
        end
        dtemp=NodeVector(i+k+1)-NodeVector(i+1);
        if(dtemp==0) 
            beta=0;
        else
            beta=(NodeVector(i+k+1)-u)/dtemp;
        end
        val1=alpha*BaseFunction(i, k-1, u, NodeVector);
        val2=beta*BaseFunction(i+1, k-1, u, NodeVector);
        val=val1+val2;
    end
end
end 