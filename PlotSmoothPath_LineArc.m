function PlotSmoothPath_LineArc()
% 光顺路径点计算与显示 - 直线与圆弧过渡
% 基于B样条曲线计算光顺后的路径点并进行可视化显示
% 支持直线与圆弧的光顺过渡

%% 读取输入数据
disp('请选择包含路径数据的文件（.txt 格式）：');
[filename_load, pathname_load] = uigetfile('*.txt', '选择路径文件');

if filename_load == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath = fullfile(pathname_load, filename_load);

% 打开文件
fileID = fopen(fullPath, 'r');
if fileID == -1
    disp('无法打开文件');
    return;
end

% 初始化路径段数组
segments = [];
segment_types = []; % 1表示直线段，2表示圆弧段
current_point = [0, 0]; % 当前点坐标

% 按行读取文件内容，并解析G代码
while ~feof(fileID)
    % 读取一行数据
    line = fgetl(fileID);
    if ~ischar(line) 
        continue;
    end
    
    % 去除前后空格
    line = strtrim(line);
    if isempty(line)
        continue;
    end
    
    % 检测G代码类型
    is_g01 = ~isempty(regexp(line, 'G0*1', 'once'));
    is_g02 = ~isempty(regexp(line, 'G0*2', 'once'));
    is_g03 = ~isempty(regexp(line, 'G0*3', 'once'));
    
    % 提取坐标
    x_match = regexp(line, 'X([-\d.]+)', 'tokens');
    y_match = regexp(line, 'Y([-\d.]+)', 'tokens');
    
    % 提取圆弧参数
    r_match = regexp(line, 'R([-\d.]+)', 'tokens');
    i_match = regexp(line, 'I([-\d.]+)', 'tokens');
    j_match = regexp(line, 'J([-\d.]+)', 'tokens');
    
    if ~isempty(x_match) && ~isempty(y_match)
        x = str2double(x_match{1}{1});
        y = str2double(y_match{1}{1});
        
        % 创建新的路径段 - 预先定义所有可能的字段以确保结构体一致性
        new_segment = struct('type', 0, ...
                             'start_point', current_point, ...
                             'end_point', [x, y], ...
                             'direction', 0, ...
                             'radius', 0, ...
                             'center', [0, 0], ...
                             'start_angle', 0, ...
                             'end_angle', 0, ...
                             'sweep_angle', 0);
        
        if is_g01 % 直线段
            new_segment.type = 1; % 直线类型
            
        elseif is_g02 || is_g03 % 圆弧段
            new_segment.type = 2; % 圆弧类型
            new_segment.direction = is_g02 + 2 * is_g03; % 1表示顺时针(G02)，2表示逆时针(G03)
            
            % 确定圆心
            if ~isempty(r_match) % 使用半径R格式
                r = str2double(r_match{1}{1});
                new_segment.radius = abs(r);
                
                % 计算圆心
                [center_x, center_y] = calculateArcCenter(current_point(1), current_point(2), x, y, r, new_segment.direction);
                new_segment.center = [center_x, center_y];
                
            elseif ~isempty(i_match) && ~isempty(j_match) % 使用I,J格式
                i_val = str2double(i_match{1}{1});
                j_val = str2double(j_match{1}{1});
                
                % 圆心是相对于起点的偏移
                center_x = current_point(1) + i_val;
                center_y = current_point(2) + j_val;
                new_segment.center = [center_x, center_y];
                
                % 计算半径
                new_segment.radius = sqrt((center_x - current_point(1))^2 + (center_y - current_point(2))^2);
            end
            
            % 计算起点和终点相对于圆心的角度
            start_angle = atan2(current_point(2) - new_segment.center(2), current_point(1) - new_segment.center(1));
            end_angle = atan2(y - new_segment.center(2), x - new_segment.center(1));
            
            % 确保角度在正确的范围内
            if new_segment.direction == 1 % 顺时针
                if end_angle > start_angle
                    end_angle = end_angle - 2*pi;
                end
            else % 逆时针
                if end_angle < start_angle
                    end_angle = end_angle + 2*pi;
                end
            end
            
            new_segment.start_angle = start_angle;
            new_segment.end_angle = end_angle;
            new_segment.sweep_angle = abs(end_angle - start_angle);
        end
        
        % 添加新段到数组
        if isempty(segments)
            segments = new_segment;
        else
            segments(end+1) = new_segment;
        end
        
        segment_types = [segment_types; new_segment.type];
        
        % 更新当前点
        current_point = [x, y];
    end
end

% 关闭文件
fclose(fileID);

% 检查是否成功读取路径段
if isempty(segments)
    disp('文件中没有有效的路径段数据');
    return;
else
    disp('文件读取成功');
    disp(['提取的路径段数量: ', num2str(length(segments))]);
end

%% 设置光顺参数
desired_error = input('请输入期望的加工误差 (默认值 0.03)：');
if isempty(desired_error)  % 如果用户直接按回车，使用默认值
    desired_error = 0.03;
end

%% 设置字体大小参数
titleFontSize = 16;       % 标题字体大小
axisFontSize = 14;        % 坐标轴标签字体大小
annotationFontSize = 12;  % 误差标注字体大小
legendFontSize = 12;      % 图例字体大小

%% 创建图形窗口
figure('Name', '路径光顺可视化', 'NumberTitle', 'off', 'Position', [100, 100, 1000, 750]);
hold on;
grid on;
axis equal;
title('路径光顺可视化', 'FontSize', titleFontSize);
xlabel('X坐标', 'FontSize', axisFontSize);
ylabel('Y坐标', 'FontSize', axisFontSize);

% 设置坐标轴字体大小
set(gca, 'FontSize', axisFontSize-2);

%% 绘制原始路径
for i = 1:length(segments)
    segment = segments(i);
    if segment.type == 1 % 直线段
        plot([segment.start_point(1), segment.end_point(1)], ...
             [segment.start_point(2), segment.end_point(2)], 'b-', 'LineWidth', 1.5, 'HandleVisibility','off');
    else % 圆弧段
        % 计算圆弧点
        angles = linspace(segment.start_angle, segment.end_angle, 100);
        arc_x = segment.center(1) + segment.radius * cos(angles);
        arc_y = segment.center(2) + segment.radius * sin(angles);
        plot(arc_x, arc_y, 'b-', 'LineWidth', 1.5, 'HandleVisibility','off');
    end
    
    % 标记端点
    plot(segment.start_point(1), segment.start_point(2), 'bo', 'MarkerSize', 5, 'HandleVisibility','off');
    if i == length(segments)
        plot(segment.end_point(1), segment.end_point(2), 'bo', 'MarkerSize', 5, 'HandleVisibility','off');
    end
end

% 为图例添加原始路径样式
plot(NaN, NaN, 'b-', 'LineWidth', 1.5);

%% 寻找直线与圆弧的交点
connection_points = [];
for i = 1:length(segments)-1
    if (segments(i).type == 1 && segments(i+1).type == 2) || ...
       (segments(i).type == 2 && segments(i+1).type == 1)
        % 这是一个直线-圆弧或圆弧-直线的连接点
        connection_point = [i, segments(i).end_point];
        
        % 如果是第一个连接点，直接赋值
        if isempty(connection_points)
            connection_points = connection_point;
        else
            % 否则添加到现有数组
            connection_points(end+1,:) = connection_point;
        end
        
        % 标记连接点
        plot(segments(i).end_point(1), segments(i).end_point(2), 'gs', 'MarkerSize', 10, 'MarkerFaceColor', 'g', 'HandleVisibility','off');
    end
end

% 为图例添加直线-圆弧连接点样式
plot(NaN, NaN, 'gs', 'MarkerSize', 10, 'MarkerFaceColor', 'g');

disp(['找到的直线-圆弧连接点数量: ', num2str(size(connection_points, 1))]);

%% 寻找圆弧与圆弧的交点
arc_arc_connection_points = [];
for i = 1:length(segments)-1
    if segments(i).type == 2 && segments(i+1).type == 2
        % 这是一个圆弧-圆弧的连接点
        connection_point = [i, segments(i).end_point];
        
        % 如果是第一个连接点，直接赋值
        if isempty(arc_arc_connection_points)
            arc_arc_connection_points = connection_point;
        else
            % 否则添加到现有数组
            arc_arc_connection_points(end+1,:) = connection_point;
        end
        
        % 标记连接点
        plot(segments(i).end_point(1), segments(i).end_point(2), 'bs', 'MarkerSize', 10, 'MarkerFaceColor', 'b', 'HandleVisibility','off');
    end
end

% 为图例添加圆弧-圆弧连接点样式
plot(NaN, NaN, 'bs', 'MarkerSize', 10, 'MarkerFaceColor', 'b');

disp(['找到的圆弧-圆弧连接点数量: ', num2str(size(arc_arc_connection_points, 1))]);

%% 对每个直线-圆弧连接点进行光顺处理
for i = 1:size(connection_points, 1)
    segment_idx = connection_points(i, 1);
    
    % 确定哪个是直线段，哪个是圆弧段
    if segments(segment_idx).type == 1 % 当前是直线段，下一个是圆弧段
        line_segment = segments(segment_idx);
        arc_segment = segments(segment_idx + 1);
        line_to_arc = true;
    else % 当前是圆弧段，下一个是直线段
        arc_segment = segments(segment_idx);
        line_segment = segments(segment_idx + 1);
        line_to_arc = false;
    end
    
    % 执行直线-圆弧光顺
    [ctrls_pos, theta_e, lp_s] = lineArcSmoothing(desired_error, line_segment, arc_segment, line_to_arc);
    
    % 绘制光顺曲线和显示误差
    plotBsplineAndError(ctrls_pos, segments(segment_idx).end_point, theta_e, lp_s, annotationFontSize);
end

%% 对每个圆弧-圆弧连接点进行光顺处理
for i = 1:size(arc_arc_connection_points, 1)
    segment_idx = arc_arc_connection_points(i, 1);
    
    % 获取两个圆弧段
    arc_segment1 = segments(segment_idx);
    arc_segment2 = segments(segment_idx + 1);
    
    % 执行圆弧-圆弧光顺
    [ctrls_pos, theta_s_max, theta_e_max] = arcArcSmoothing(desired_error, arc_segment1, arc_segment2);
    
    % 绘制光顺曲线和显示误差
    plotBsplineAndError(ctrls_pos, segments(segment_idx).end_point, theta_s_max, theta_e_max, annotationFontSize, true);
end

% 更新图例
legend({'原始路径', '直线-圆弧拐角', '圆弧-圆弧拐角', '光顺曲线', '控制多边形'}, ...
       'Location', 'best', 'FontSize', legendFontSize);

% 更新标题
title(sprintf('路径光顺可视化 (期望误差: %.4f)', desired_error), 'FontSize', titleFontSize);

end

%% 直线-圆弧光顺函数
function [ctrls_pos, theta_emax, lp_max] = lineArcSmoothing(desired_error, line_segment, arc_segment, line_to_arc)
    % 提取几何参数
    if line_to_arc
        p_i = line_segment.end_point;
        v_line = p_i - line_segment.start_point;
        m_line = v_line / norm(v_line);
        L_p = norm(v_line);
        r = arc_segment.radius;
        Theta = arc_segment.sweep_angle;
    else
        p_i = arc_segment.end_point;
        r = arc_segment.radius;
        v_line = line_segment.end_point - p_i;
        m_line = v_line / norm(v_line);
        L_p = norm(v_line);
        Theta = arc_segment.sweep_angle;
    end

    % === 核心修正 1：修正m_arc切线方向 ===
    center = arc_segment.center;
    v_arc = p_i - center;
    if arc_segment.direction == 2 % 逆时针 (G03)
        m_arc = [-v_arc(2), v_arc(1)] / r; % 正确的逆时针切线
    else % 顺时针 (G02)
        m_arc = [v_arc(2), -v_arc(1)] / r; % 正确的顺时针切线
    end
    % === 修正结束 ===

    % 计算φ角度 - 直线与圆弧切线的夹角
    cos_phi = dot(m_line, m_arc);
    
    % === 核心修正 2：检测180度拐角并选择误差模型 ===
    TOLERANCE = -0.99; % 定义一个允差，用于判断是否接近180度
    
    if cos_phi < TOLERANCE
        % 情况1：检测到接近180度拐角 (尖点)
        disp('检测到接近180度拐角，切换到直接误差计算模型。');
        % 使用更直接的误差计算函数
        epsilon_model = @(theta) calculate_error_at_cusp(theta, p_i, m_line, r, line_to_arc, arc_segment);
    else
        % 情况2：普通拐角
        phi = acos(max(min(cos_phi, 1), -1));
        % 继续使用论文中的解析误差模型
        f = @f_theta;
        epsilon_model = @(theta) 0.125 * r * f(theta) * cos(phi/2);
    end
    % === 修正结束 ===
    
    % 特征函数f(θ)及其反函数
    f = @f_theta;
    f_inv = @(y) fzero(@(theta) f_theta(theta) - y, [0.001, pi/2-1e-6]);
    
    % 计算θ̂_{e,i}，根据论文公式(19)
    try
        theta_e_hat = min([0.3125 * Theta, f_inv(1.1 * L_p / r), 4*pi/9]);
    catch
        theta_e_hat = min([0.3125 * Theta, 4*pi/9]); % fzero找不到解时的备用方案
    end
    
    % === 核心修改：目标误差求解 ===
    % 计算最大光顺角度对应的最大误差
    max_possible_error = epsilon_model(theta_e_hat);
    
    % 判断期望误差是否可行，并求解最终光顺角度
    if desired_error > max_possible_error
        warning('期望误差 %.4f 大于几何约束下的最大可达误差 %.4f。将使用最大误差进行光顺。', desired_error, max_possible_error);
        theta_emax = theta_e_hat;
    else
        % 使用二分法求解产生目标误差的光顺角度
        theta_emax = bisectionMethod(epsilon_model, 0.001, theta_e_hat, desired_error, 1e-7);
    end
    % === 修改结束 ===
    
    % 计算直线侧光顺长度l_{p,i}，根据论文公式(17)
    lp_max = (5*r/11) * f(theta_emax);
    
    % 根据论文公式(14)，确保l_{p,i}不超过最大允许值
    % 公式(14)中的分母应为(5*c_l1 + c_l2)*cos(phi/2)
    c_l1 = 0.35; c_l2 = 0.45; % 比例常数，与控制点计算中的值一致
    
    % === 核心修正 3：避免在180度拐角时除以零 ===
    if cos_phi < TOLERANCE
        % 对于接近180度的拐角，使用更保守的限制
        lp_max_limit = min([0.3125*L_p, 0.3125*L_p]);
    else
        phi = acos(max(min(cos_phi, 1), -1));
        lp_max_limit = min([0.3125*L_p, 0.3125*L_p, 8*desired_error/((5*c_l1 + c_l2)*cos(phi/2))]);
    end
    % === 修正结束 ===
    
    lp_max = min(lp_max, lp_max_limit);
    
    % 调用控制点计算函数
    ctrls_pos = calculateControlPoints_Paper_Final(p_i, m_line, r, theta_emax, lp_max, line_to_arc, arc_segment);
end

%% 特征函数f(θ)的实现
function val = f_theta(theta)
    % 根据论文公式(16)实现特征函数f(θ)
    % a项使用sin, b项使用cos
    a = sin(theta/2) * (10*sin(theta/2) - 5.6*sin(theta) + 1.6*sin(theta/2)*cos(theta));
    b = sin(theta/2) * (10*cos(theta/2) - 5.6*cos(theta) + 1.6*sin(theta/2)*sin(theta));
    val = sqrt(a^2 + b^2);
end

%% 二分法求解最优角度
function theta_opt = bisectionMethod(error_func, a_s, a_e, target_error, epsilon_c)
% 使用二分法求解最优光顺角度，针对目标误差进行优化
% 输入:
%   error_func: 误差函数ε_{p,i}(θ)
%   a_s, a_e: 搜索区间[a_s, a_e]
%   target_error: 目标误差ε_{p}
%   epsilon_c: 计算精度
% 输出:
%   theta_opt: 最优光顺角度θ_{max,i}

% 检查边界情况
error_at_as = error_func(a_s);
error_at_ae = error_func(a_e);

% 如果目标误差超出范围，返回边界值
if target_error <= error_at_as
    theta_opt = a_s;
    return;
end

if target_error >= error_at_ae
    theta_opt = a_e;
    return;
end

% 初始化
a_l = a_s;  % 左边界
a_r = a_e;  % 右边界

% 循环直到达到所需精度
while (a_r - a_l) > epsilon_c
    % 计算中点
    theta_mid = (a_l + a_r) / 2;
    
    % 计算误差
    error_val = error_func(theta_mid);
    
    % 根据误差值更新搜索区间
    if error_val < target_error
        a_l = theta_mid;  % 如果误差小于目标，移动左边界
    else
        a_r = theta_mid;  % 如果误差大于目标，移动右边界
    end
end

% 返回最终结果
theta_opt = (a_l + a_r) / 2;
end

%% 论文中的控制点计算方法 (最终修正版)
function ctrls_pos = calculateControlPoints_Paper_Final(p_i, m_line, r, theta_e, lp_s, line_to_arc, arc_segment)
    % 初始化
    ctrls_pos = zeros(2, 7);
    d3 = p_i';
    ctrls_pos(:, 4) = d3;
    center = arc_segment.center';
    arc_direction = arc_segment.direction;
    c_l1 = 0.35; c_l2 = 0.45; c_l3 = 0.2;

    if line_to_arc
        %% CASE 1: 直线 -> 圆弧
        % 1. 直线侧控制点 d0, d1, d2 (基于公式4和11)
        ctrls_pos(:, 3) = d3 - c_l1 * lp_s * m_line';
        ctrls_pos(:, 2) = ctrls_pos(:, 3) - c_l2 * lp_s * m_line';
        ctrls_pos(:, 1) = ctrls_pos(:, 2) - c_l3 * lp_s * m_line';

        % 2. 圆弧侧控制点 d4, d5, d6 (基于公式9)
        angle_at_d3 = atan2(p_i(2) - center(2), p_i(1) - center(1));
        if arc_direction == 2 % 逆时针 G03
            angle_at_d6 = angle_at_d3 + theta_e;
        else % 顺时针 G02
            angle_at_d6 = angle_at_d3 - theta_e;
        end
        d6 = center + r * [cos(angle_at_d6); sin(angle_at_d6)];
        
        if arc_direction == 2 % 逆时针 G03
            m_e = [-sin(angle_at_d6); cos(angle_at_d6)];
        else % 顺时针 G02
            m_e = [sin(angle_at_d6); -cos(angle_at_d6)];
        end

        vec_d6_c = d6 - center;
        d5 = d6 - (0.4 * r * sin(theta_e)) * m_e;
        d4 = (0.8 + 0.2*cos(theta_e))*vec_d6_c - (0.57*r*sin(theta_e))*m_e + center;
        
        ctrls_pos(:, 7) = d6;
        ctrls_pos(:, 6) = d5;
        ctrls_pos(:, 5) = d4;

    else
        %% CASE 2: 圆弧 -> 直线
        % 1. 直线侧控制点 d4, d5, d6 (基于公式4和11)
        ctrls_pos(:, 5) = d3 + c_l1 * lp_s * m_line';
        ctrls_pos(:, 6) = ctrls_pos(:, 5) + c_l2 * lp_s * m_line';
        ctrls_pos(:, 7) = ctrls_pos(:, 6) + c_l3 * lp_s * m_line';

        % 2. 圆弧侧控制点 d0, d1, d2 (基于公式10)
        angle_at_d3 = atan2(p_i(2) - center(2), p_i(1) - center(1));
        if arc_direction == 2 % 逆时针 G03
            angle_at_d0 = angle_at_d3 - theta_e;
        else % 顺时针 G02
            angle_at_d0 = angle_at_d3 + theta_e;
        end
        d0 = center + r * [cos(angle_at_d0); sin(angle_at_d0)];
        
        if arc_direction == 2 % 逆时针 G03
            m_s = [-sin(angle_at_d0); cos(angle_at_d0)];
        else % 顺时针 G02
            m_s = [sin(angle_at_d0); -cos(angle_at_d0)];
        end

        vec_d0_c = d0 - center;
        d1 = d0 + (0.4 * r * sin(theta_e)) * m_s;
        d2 = (0.8 + 0.2*cos(theta_e))*vec_d0_c + (0.57*r*sin(theta_e))*m_s + center;
        
        ctrls_pos(:, 1) = d0;
        ctrls_pos(:, 2) = d1;
        ctrls_pos(:, 3) = d2;
    end
end

%% 计算圆弧中心点
function [center_x, center_y] = calculateArcCenter(x1, y1, x2, y2, r, direction)
% 根据起点、终点、半径和方向计算圆弧中心点
% 输入:
%   x1, y1: 起点坐标
%   x2, y2: 终点坐标
%   r: 半径
%   direction: 1表示顺时针(G02)，2表示逆时针(G03)
% 输出:
%   center_x, center_y: 圆心坐标

% 计算起点和终点之间的中点
mid_x = (x1 + x2) / 2;
mid_y = (y1 + y2) / 2;

% 计算起点和终点之间的距离
chord = sqrt((x2 - x1)^2 + (y2 - y1)^2);

% 检查半径是否足够大
if abs(r) < chord/2
    error('半径太小，无法创建圆弧');
end

% 计算从中点到圆心的距离
h = sqrt(r^2 - (chord/2)^2);

% 计算起点到终点的向量
dx = x2 - x1;
dy = y2 - y1;

% 单位化
len = sqrt(dx^2 + dy^2);
dx = dx / len;
dy = dy / len;

% 计算垂直于起点到终点向量的单位向量
% 对于G02(顺时针)和G03(逆时针)，垂直方向相反
if direction == 1 % 顺时针(G02)
    nx = dy;   % 向右旋转90度
    ny = -dx;
else % 逆时针(G03)
    nx = -dy;  % 向左旋转90度
    ny = dx;
end

% 计算圆心
center_x = mid_x + h * nx;
center_y = mid_y + h * ny;

% 验证计算结果
r1 = sqrt((center_x - x1)^2 + (center_y - y1)^2);
r2 = sqrt((center_x - x2)^2 + (center_y - y2)^2);

% 检查计算的圆心是否满足条件
if abs(r1 - abs(r)) > 1e-6 || abs(r2 - abs(r)) > 1e-6
    warning('计算的圆心可能不准确，请检查。');
    fprintf('计算的半径: r1=%.6f, r2=%.6f, 期望半径: r=%.6f\n', r1, r2, abs(r));
    
    % 尝试调整圆心位置，使其满足半径条件
    % 计算圆心到起点和终点的单位向量
    v1 = [x1 - center_x, y1 - center_y] / r1;
    v2 = [x2 - center_x, y2 - center_y] / r2;
    
    % 调整圆心位置
    center_x = (x1 - r * v1(1) + x2 - r * v2(1)) / 2;
    center_y = (y1 - r * v1(2) + y2 - r * v2(2)) / 2;
    
    % 重新验证
    r1 = sqrt((center_x - x1)^2 + (center_y - y1)^2);
    r2 = sqrt((center_x - x2)^2 + (center_y - y2)^2);
    fprintf('调整后的半径: r1=%.6f, r2=%.6f\n', r1, r2);
end

% 验证方向是否正确
% 计算起点和终点相对于圆心的角度
angle1 = atan2(y1 - center_y, x1 - center_x);
angle2 = atan2(y2 - center_y, x2 - center_x);

% 根据方向调整角度，使其在正确的范围内
if direction == 1 % 顺时针(G02)
    if angle2 > angle1
        angle2 = angle2 - 2*pi;
    end
    sweep_angle = angle1 - angle2;
else % 逆时针(G03)
    if angle2 < angle1
        angle2 = angle2 + 2*pi;
    end
    sweep_angle = angle2 - angle1;
end

% 如果计算出的弧度小于0或大于π，可能圆心方向错误
if sweep_angle < 0 || sweep_angle > pi
    % 尝试反向计算
    center_x = mid_x - h * nx;
    center_y = mid_y - h * ny;
    
    % 重新验证
    r1 = sqrt((center_x - x1)^2 + (center_y - y1)^2);
    r2 = sqrt((center_x - x2)^2 + (center_y - y2)^2);
    
    if abs(r1 - abs(r)) > 1e-6 || abs(r2 - abs(r)) > 1e-6
        warning('反向计算的圆心也可能不准确，请检查。');
    end
end
end

%% B样条曲线计算函数
function smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector)
% 计算B样条曲线上的一系列点
% 参数:
%   ctrls_pos: 控制点坐标，每列是一个控制点的[x;y]坐标
%   k: B样条次数
%   NodeVector: 节点矢量

% 设置采样点数量
num_samples = 100;
u_values = linspace(0, 1, num_samples);

% 初始化结果数组
smooth_points = zeros(2, num_samples);

% 计算每个参数值对应的点
for idx = 1:num_samples
    u = u_values(idx);
    smooth_points(:, idx) = computeBsplinePoint(ctrls_pos, u, k, NodeVector);
end
end

function p = computeBsplinePoint(ctrls_pos, u, k, NodeVector)
% 计算B样条上参数u对应的点
n = size(ctrls_pos, 2) - 1;
Nik = zeros(n+1, 1);

for i1 = 0:n
    Nik(i1+1, 1) = BaseFunction(i1+1, k, u, NodeVector);
end

p = ctrls_pos * Nik; % 计算样条上的点
end

function val = BaseFunction(i, k, u, NodeVector)
% B样条基函数
% i为控制点序号；k为B样条次数；u为样条参数；NodeVector为节点矢量
if k==0    
    if abs(u-NodeVector(end))<10^-6
        if NodeVector(i)<=u && abs(u-NodeVector(i+1))<10^-6
            val=1;return;
        else
            val=0;return;
        end
    else
        if(u<NodeVector(i)||u>=NodeVector(i+1))
            val=0;return;
        else
            val=1;return;
        end
    end
end

if k>0
    if(u<NodeVector(i)||u>NodeVector(i+k+1)) 
        val=0;
    else
        dtemp=NodeVector(i+k)-NodeVector(i);
        if(dtemp==0) 
            alpha=0;
        else
            alpha=(u-NodeVector(i))/dtemp;
        end
        dtemp=NodeVector(i+k+1)-NodeVector(i+1);
        if(dtemp==0) 
            beta=0;
        else
            beta=(NodeVector(i+k+1)-u)/dtemp;
        end
        val1=alpha*BaseFunction(i, k-1, u, NodeVector);
        val2=beta*BaseFunction(i+1, k-1, u, NodeVector);
        val=val1+val2;
    end
end
end

%% 圆弧-圆弧光顺函数
function [ctrls_pos, theta_s_max, theta_e_max] = arcArcSmoothing(desired_error, arc_segment1, arc_segment2)
% 圆弧-圆弧光顺算法
% desired_error: 用户输入的目标误差 ε_p
% arc_segment1: 第一个圆弧段 (对应论文中的第i段)
% arc_segment2: 第二个圆弧段 (对应论文中的第i+1段)
% ctrls_pos: 计算出的7个B样条控制点
% theta_s_max, theta_e_max: 计算出的最优光顺角度

% 1. 提取几何参数
% 连接点(角隅点) p_i
p_i = arc_segment1.end_point;

% 第一个圆弧(s-side)的参数
r_s = arc_segment1.radius;        % 半径 r_i
Theta_s = arc_segment1.sweep_angle; % 总扫描角 Θ_i

% 第二个圆弧(e-side)的参数
r_e = arc_segment2.radius;        % 半径 r_{i+1}
Theta_e = arc_segment2.sweep_angle; % 总扫描角 Θ_{i+1}

% 特征函数及其反函数 (使用主程序中定义的函数)
f = @f_theta;
f_inv = @(y) fzero(@(theta) f_theta(theta) - y, [0.001, pi/2-1e-6]);

% 2. 计算初始光顺角度 theta_e_hat
% 使用 try-catch 结构以防 fzero 找不到解
try
    % 论文公式(19)，上半部分
    val_for_finv = r_s * f(0.5 * Theta_s) / r_e;
    theta_from_f = f_inv(val_for_finv);
    theta_e_hat = min([0.3125 * Theta_e, theta_from_f, 4*pi/9]);
catch
    % fzero 求解失败时的备用方案
    theta_e_hat = min(0.3125 * Theta_e, 4*pi/9);
end

% 3. 建立并求解误差模型
% 建立 θ_s 和 θ_e 的关系: 根据论文公式(16)
get_theta_s = @(theta_e) f_inv(r_e * f(theta_e) / r_s);

% 计算切线夹角 phi
tangent_s = get_tangent_at_point(arc_segment1, p_i);
tangent_e = get_tangent_at_point(arc_segment2, p_i);
cos_phi = dot(tangent_s, tangent_e);

% === 核心修改：检测180度拐角并选择误差模型 ===
TOLERANCE = -0.99; % 定义一个允差，用于判断是否接近180度

if cos_phi < TOLERANCE
    % 情况1：检测到接近180度拐角 (尖点)
    disp('arcArcSmoothing: 检测到接近180度拐角，切换到直接误差计算模型。');
    % 使用更直接的误差计算函数
    epsilon_model = @(theta_e) calculate_error_at_cusp_arc_arc(theta_e, p_i, arc_segment1, arc_segment2);
else
    % 情况2：普通拐角
    phi = acos(max(min(cos_phi, 1), -1));
    % 继续使用论文中的解析误差模型
    epsilon_model = @(theta_e) 0.125 * r_e * f(theta_e) * cos(phi/2);
end
% === 修改结束 ===

% === 核心修改：目标误差求解 ===
% 计算最大光顺角度对应的最大误差
max_possible_error = epsilon_model(theta_e_hat);

% 判断期望误差是否可行，并求解最终光顺角度
if desired_error > max_possible_error
    warning('期望误差 %.4f 大于几何约束下的最大可达误差 %.4f。将使用最大误差进行光顺。', desired_error, max_possible_error);
    theta_e_max = theta_e_hat;
else
    % 使用二分法求解产生目标误差的光顺角度
    theta_e_max = bisectionMethod(epsilon_model, 0.001, theta_e_hat, desired_error, 1e-7);
end
% === 修改结束 ===

% 计算对应的 theta_s_max
theta_s_max = get_theta_s(theta_e_max);

% 4. 计算控制点
ctrls_pos = calculateControlPoints_ArcArc(p_i, arc_segment1, arc_segment2, theta_s_max, theta_e_max);
end

function tangent = get_tangent_at_point(arc_segment, point)
% 计算圆弧上某点的单位切线向量
center = arc_segment.center;
radius_vector = point - center;
radius_vector = radius_vector / norm(radius_vector);  % 单位化

% 根据圆弧方向确定切线方向
if arc_segment.direction == 2  % 逆时针 G03
    tangent = [-radius_vector(2), radius_vector(1)];  % 逆时针旋转90度
else  % 顺时针 G02
    tangent = [radius_vector(2), -radius_vector(1)];  % 顺时针旋转90度
end

% 确保返回单位向量
tangent = tangent / norm(tangent);
end

function ctrls_pos = calculateControlPoints_ArcArc(p_i, arc1, arc2, theta_s, theta_e)
% 计算圆弧-圆弧光顺的B样条控制点
% p_i: 角隅点坐标 [x; y]
% arc1: 第一个圆弧段结构体
% arc2: 第二个圆弧段结构体
% theta_s: 第一个圆弧的光顺角度
% theta_e: 第二个圆弧的光顺角度

% 初始化控制点数组 (2x7)
ctrls_pos = zeros(2, 7);

% 中心控制点 d3 即为角隅点 p_i
ctrls_pos(:, 4) = p_i';

% 计算第一段圆弧侧(s-side)的控制点 d0, d1, d2
% s-side (arc1)
center_s = arc1.center';
angle_at_d3_s = atan2(p_i(2) - center_s(2), p_i(1) - center_s(1));

% 根据圆弧方向回退角度
if arc1.direction == 2 % 逆时针 G03
    angle_at_d0 = angle_at_d3_s - theta_s;
else % 顺时针 G02
    angle_at_d0 = angle_at_d3_s + theta_s;
end

% 计算 d0 点坐标
d0 = center_s + arc1.radius * [cos(angle_at_d0); sin(angle_at_d0)];
ctrls_pos(:, 1) = d0;

% 计算 d0 处的切线向量 m_s
% 对于圆弧，切线方向与半径向量垂直
vec_d0_c = d0 - center_s;
if arc1.direction == 2 % 逆时针 G03
    m_s = [-vec_d0_c(2); vec_d0_c(1)] / norm(vec_d0_c);
else % 顺时针 G02
    m_s = [vec_d0_c(2); -vec_d0_c(1)] / norm(vec_d0_c);
end

% 根据论文公式(10)计算 d1 和 d2
d1 = d0 + (0.4 * arc1.radius * sin(theta_s)) * m_s;
d2 = (0.8 + 0.2*cos(theta_s))*vec_d0_c + (0.8 * arc1.radius * sin(theta_s))*m_s + center_s;

ctrls_pos(:, 2) = d1;
ctrls_pos(:, 3) = d2;

% 计算第二段圆弧侧(e-side)的控制点 d4, d5, d6
% e-side (arc2)
center_e = arc2.center';
angle_at_d3_e = atan2(p_i(2) - center_e(2), p_i(1) - center_e(1));

% 根据圆弧方向前进角度
if arc2.direction == 2 % 逆时针 G03
    angle_at_d6 = angle_at_d3_e + theta_e;
else % 顺时针 G02
    angle_at_d6 = angle_at_d3_e - theta_e;
end

% 计算 d6 点坐标
d6 = center_e + arc2.radius * [cos(angle_at_d6); sin(angle_at_d6)];
ctrls_pos(:, 7) = d6;

% 计算 d6 处的切线向量 m_e
vec_d6_c = d6 - center_e;
if arc2.direction == 2 % 逆时针 G03
    m_e = [-vec_d6_c(2); vec_d6_c(1)] / norm(vec_d6_c);
else % 顺时针 G02
    m_e = [vec_d6_c(2); -vec_d6_c(1)] / norm(vec_d6_c);
end

% 根据论文公式(9)计算 d4 和 d5
d5 = d6 - (0.4 * arc2.radius * sin(theta_e)) * m_e;
d4 = (0.8 + 0.2*cos(theta_e))*vec_d6_c - (0.8 * arc2.radius * sin(theta_e))*m_e + center_e;

ctrls_pos(:, 5) = d4;
ctrls_pos(:, 6) = d5;
end

% === 新增辅助函数：在文件末尾添加 ===
function error = calculate_error_at_cusp(theta_e, p_i, m_line, r, line_to_arc, arc_segment)
    % 180度拐角(尖点)情况下的直接误差计算函数
    % 输入:
    %   theta_e: 当前用于测试的光顺角度
    %   p_i, m_line, r, ...: 原始的几何参数
    % 输出:
    %   error: 根据theta_e计算出的真实误差
    
    % 1. 根据theta_e计算对应的lp_s
    f = @f_theta;
    lp_s = (5*r/11) * f(theta_e);
    
    % 2. 计算B样条的控制点
    ctrls_pos = calculateControlPoints_Paper_Final(p_i, m_line, r, theta_e, lp_s, line_to_arc, arc_segment);
    
    % 3. 计算u=0.5时的B样条曲线上的点
    k = 5; % B样条次数
    NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
    mid_point = computeBsplinePoint(ctrls_pos, 0.5, k, NodeVector);
    
    % 4. 直接计算该点与拐角的距离作为误差
    error = norm(p_i' - mid_point);
end

% === 添加新的辅助函数 ===
function error = calculate_error_at_cusp_arc_arc(theta_e, p_i, arc1, arc2)
    % 180度拐角(尖点)情况下的直接误差计算函数 - 圆弧-圆弧版本
    % 输入:
    %   theta_e: 当前用于测试的光顺角度
    %   p_i: 拐角点坐标
    %   arc1, arc2: 两个圆弧段
    % 输出:
    %   error: 根据theta_e计算出的真实误差
    
    % 1. 计算对应的theta_s
    r_s = arc1.radius; r_e = arc2.radius;
    f = @f_theta;
    f_inv = @(y) fzero(@(theta) f_theta(theta) - y, [0.001, pi/2-1e-6]);
    get_theta_s = @(theta_e_val) f_inv(r_e * f(theta_e_val) / r_s);
    theta_s = get_theta_s(theta_e);
    
    % 2. 计算B样条的控制点
    ctrls_pos = calculateControlPoints_ArcArc(p_i, arc1, arc2, theta_s, theta_e);
    
    % 3. 计算u=0.5时的B样条曲线上的点
    k = 5; % B样条次数
    NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
    mid_point = computeBsplinePoint(ctrls_pos, 0.5, k, NodeVector);
    
    % 4. 直接计算该点与拐角的距离作为误差
    error = norm(p_i' - mid_point);
end

%% 绘制B样条曲线和误差
function plotBsplineAndError(ctrls_pos, connection_point, param1, param2, fontSize, is_arc_arc)
    % 绘制B样条曲线和显示误差
    % 输入:
    %   ctrls_pos: 控制点坐标
    %   connection_point: 拐角点坐标
    %   param1, param2: 光顺参数 (theta_e和lp_s或theta_s和theta_e)
    %   fontSize: 标注字体大小
    %   is_arc_arc: 是否为圆弧-圆弧光顺 (默认为false)
    
    if nargin < 6
        is_arc_arc = false;
    end
    
    % 定义B样条参数
    k = 5; % B样条次数为5，确保G³连续性
    NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1]; % 5次B样条需要6个重复节点
    
    % 计算光顺曲线上的点
    smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector);
    
    % 绘制光顺曲线
    plot(smooth_points(1,:), smooth_points(2,:), 'g-', 'LineWidth', 2);
    
    % 绘制控制点和控制多边形
    plot(ctrls_pos(1,:), ctrls_pos(2,:), 'm--', 'LineWidth', 1);
    plot(ctrls_pos(1,:), ctrls_pos(2,:), 'mo', 'MarkerSize', 4, 'HandleVisibility','off');
    
    % 计算u=0.5时的点（光顺曲线中点）
    mid_point = computeBsplinePoint(ctrls_pos, 0.5, k, NodeVector);
    
    % 计算中点到拐角的距离（加工误差）
    error_distance = norm(mid_point - connection_point');
    
    % 在中点和拐角间绘制线段，表示误差
    plot([mid_point(1), connection_point(1)], [mid_point(2), connection_point(2)], 'r--', 'LineWidth', 1, 'HandleVisibility','off');
    
    % 在中点标记
    plot(mid_point(1), mid_point(2), 'r*', 'MarkerSize', 8, 'HandleVisibility','off');
    
    % 显示光顺参数和误差距离
    if is_arc_arc
        text_str = sprintf('θs=%.2f°, θe=%.2f°\n误差=%.4f', param1*180/pi, param2*180/pi, error_distance);
    else
        text_str = sprintf('θ=%.2f°, lp=%.2f\n误差=%.4f', param1*180/pi, param2, error_distance);
    end
    text(connection_point(1)+0.1, connection_point(2)+0.1, text_str, 'FontSize', fontSize, 'Color', 'k', 'VerticalAlignment', 'bottom');
end 