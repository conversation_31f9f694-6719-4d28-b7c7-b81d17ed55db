function CompareTwoPathsSimple()
% 在同一坐标轴下比较机床实际路径和光顺路径
% 简化版本，仅显示两条路径，不计算误差统计

clear all;
close all;

%% 第一步：读取机床实际路径数据（G代码文件夹中的文件）
disp('请选择G代码文件夹中的机床实际路径数据文件（.txt 格式）：');
[filename_machine, pathname_machine] = uigetfile('G代码/*.txt', '选择机床实际路径数据');

if filename_machine == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath_machine = fullfile(pathname_machine, filename_machine);

% 读取机床数据文件
try
    machine_data = readtable(fullPath_machine, 'HeaderLines', 2, 'Delimiter', ' ', 'MultipleDelimsAsOne', true);
    disp(['成功读取机床数据文件: ', fullPath_machine]);
catch ME
    disp(['无法读取文件: ', fullPath_machine]);
    disp(['错误信息: ', ME.message]);
    return;
end

% 获取列名
colNames = machine_data.Properties.VariableNames;
disp('可用的列名:');
disp(colNames);

% 尝试找到X和Y位置数据列
xColIndex = find(contains(colNames, 'X.fActPosition'));
yColIndex = find(contains(colNames, 'Y.fActPosition'));

% 如果找不到列，尝试手动指定
if isempty(xColIndex) || isempty(yColIndex)
    % 尝试手动指定列索引（根据数据文件中的位置）
    fprintf('未能自动找到X.fActPosition和Y.fActPosition列，尝试手动指定列...\n');
    
    % 根据Data.txt文件中的位置，X.fActPosition应该是第18列，Y.fActPosition是第19列
    xColIndex = 18;
    yColIndex = 19;
    
    fprintf('手动指定X.fActPosition为第%d列，Y.fActPosition为第%d列\n', xColIndex, yColIndex);
end

% 提取X和Y坐标
x_machine = table2array(machine_data(:, xColIndex));
y_machine = table2array(machine_data(:, yColIndex));

% 移除零值点（通常是机床未启动的数据）
valid_indices = (x_machine ~= 0) | (y_machine ~= 0);
x_machine = x_machine(valid_indices);
y_machine = y_machine(valid_indices);

%% 第二步：读取G代码文件并生成光顺路径
disp(' ');
disp('请选择包含路径数据的G代码文件（.txt 格式）：');
[filename_gcode, pathname_gcode] = uigetfile('*.txt', '选择G代码文件');

if filename_gcode == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath_gcode = fullfile(pathname_gcode, filename_gcode);

% 打开文件
fileID = fopen(fullPath_gcode, 'r');
if fileID == -1
    disp('无法打开文件');
    return;
end

% 初始化 points 数组
points = [];

% 按行读取文件内容，并解析坐标数据
while ~feof(fileID)
    % 读取一行数据
    line = fgetl(fileID);
    if ischar(line) % 确保读取到的是有效行
        % 使用正则表达式提取X和Y的数值
        tokens = regexp(line, 'X([-\d.]+)\s+Y([-\d.]+)', 'tokens');
        
        if ~isempty(tokens)
            % 将提取的字符串转换为数值
            x = str2double(tokens{1}{1});
            y = str2double(tokens{1}{2});
            
            % 将坐标添加到 points 数组中
            points = [points; x, y];
        end
    end
end

% 关闭文件
fclose(fileID);

% 检查 points 是否成功读取
if isempty(points)
    disp('文件中没有有效坐标数据');
    return;
else
    disp('G代码文件读取成功');
    disp(['提取的坐标点数量: ', num2str(size(points, 1))]);
end

%% 设置光顺参数
pe = input('请输入路径光顺的最大逼近误差 (默认值 0.03)：');
if isempty(pe)  % 如果用户直接按回车，使用默认值
    pe = 0.03;
end

%% 路径分析
points = points';
num_points = size(points, 2);
num = num_points - 2; % 过渡拐角数量

% 计算路径段长度
Length = sqrt(diff(points(1,:)).^2 + diff(points(2,:)).^2);

% 计算拐角处的夹角
angle = zeros(1, num);
for i = 1:num
    v1 = points(:, i) - points(:, i+1);
    v2 = points(:, i+2) - points(:, i+1);
    
    % 计算两个向量之间的夹角（弧度）
    cos_angle = (v1' * v2) / (norm(v1) * norm(v2));
    % 确保cos_angle在[-1, 1]范围内，避免数值误差
    cos_angle = max(min(cos_angle, 1), -1);
    angle(i) = acos(cos_angle);
end

% 将弧度转换为角度
angle_degrees = angle * 180 / pi;
disp('拐角角度（度）:');
disp(angle_degrees);

%% 计算光顺路径点
% 定义B样条参数
k = 5; % B样条次数，使用5次B样条

% 初始化存储所有光顺段的点
all_smooth_points_x = [];
all_smooth_points_y = [];
original_path_x = points(1,:);
original_path_y = points(2,:);

% 处理每个拐角
for i = 1:num
    % 只对角度小于等于170度的拐角进行光顺
    if angle_degrees(i) <= 170
        % 计算控制点和光顺参数D
        [ctrls_pos, D] = localSmoothing(pe, points, Length, angle, i);
        
        % 创建节点矢量 (均匀节点矢量)
        NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
        
        % 计算光顺曲线上的点
        smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector);
        
        % 收集所有光顺点
        all_smooth_points_x = [all_smooth_points_x, smooth_points(1,:)];
        all_smooth_points_y = [all_smooth_points_y, smooth_points(2,:)];
    end
end

%% 计算每个拐角到各路径的最小距离
% 获取所有拐角点坐标
corner_points = points(:, 2:end-1); % 去掉起点和终点
num_corners = size(corner_points, 2);

% 初始化存储最小距离的数组
min_machine_distances = zeros(1, num_corners);
min_smooth_distances = zeros(1, num_corners);
% 移除G代码路径最小距离数组
% min_gcode_distances = zeros(1, num_corners);

% 对每个拐角计算最小距离
for i = 1:num_corners
    corner_point = corner_points(:, i);
    
    % 计算机床实际路径上每个点到当前拐点的距离
    machine_distances = sqrt((x_machine - corner_point(1)).^2 + (y_machine - corner_point(2)).^2);
    min_machine_distances(i) = min(machine_distances);
    
    % 计算光顺路径上每个点到当前拐点的距离
    smooth_distances = sqrt((all_smooth_points_x - corner_point(1)).^2 + (all_smooth_points_y - corner_point(2)).^2);
    min_smooth_distances(i) = min(smooth_distances);
    
    % 移除G代码路径最小距离计算
    % % 计算原始G代码路径上每个点到当前拐点的距离
    % gcode_distances = sqrt((original_path_x - corner_point(1)).^2 + (original_path_y - corner_point(2)).^2);
    % min_gcode_distances(i) = min(gcode_distances);
end

% 显示最小距离
disp('每个拐角的最小距离:');
for i = 1:num_corners
    disp(['拐角 ', num2str(i), ' (', num2str(corner_points(1,i)), ',', num2str(corner_points(2,i)), '):']);
    disp(['  机床实际路径最小距离: ', num2str(min_machine_distances(i))]);
    disp(['  B样条光顺路径最小距离: ', num2str(min_smooth_distances(i))]);
    % 移除G代码路径最小距离显示
    % disp(['  原始G代码路径最小距离: ', num2str(min_gcode_distances(i))]);
end

% 计算最大逼近误差（两条路径之间的最大差异）
max_error_machine_smooth = max(abs(min_machine_distances - min_smooth_distances));
disp(['机床路径与光顺路径之间的最大逼近误差: ', num2str(max_error_machine_smooth)]);

%% 创建图形窗口，包含路径对比图和柱状图
figure('Name', '路径对比与最小距离分析', 'NumberTitle', 'off', 'Position', [100, 100, 1200, 800]);

% 创建子图布局
subplot(1, 2, 1);

% 绘制机床实际路径
plot(x_machine, y_machine, 'r-', 'LineWidth', 1.5);
hold on;

% 绘制原始G代码路径
plot(original_path_x, original_path_y, 'b--', 'LineWidth', 1);

% 绘制B样条光顺路径
plot(all_smooth_points_x, all_smooth_points_y, 'g-', 'LineWidth', 1.5);

% 标记所有拐点
for i = 1:num_corners
    plot(corner_points(1,i), corner_points(2,i), 'ko', 'MarkerSize', 8, 'MarkerFaceColor', 'y');
    text(corner_points(1,i)+0.1, corner_points(2,i)+0.1, ['拐点', num2str(i)], 'FontSize', 10);
end

% 标记起点和终点
plot(original_path_x(1), original_path_y(1), 'ko', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
plot(original_path_x(end), original_path_y(end), 'ko', 'MarkerSize', 10, 'MarkerFaceColor', 'k');

% 添加图例
legend({'机床实际路径', '原始G代码路径', 'B样条光顺路径', '拐点', '起点/终点'}, 'Location', 'best');

% 设置坐标轴等比例
axis equal;
grid on;
title(['路径对比: ', filename_machine, ' vs ', filename_gcode], 'FontSize', 14);
xlabel('X 坐标', 'FontSize', 12);
ylabel('Y 坐标', 'FontSize', 12);

% 添加信息框
info_text = sprintf('最大逼近误差: %.3f\n机床路径点数: %d\nG代码路径点数: %d\n最大逼近误差: %.3f', ...
    pe, length(x_machine), num_points, max_error_machine_smooth);
annotation('textbox', [0.05, 0.02, 0.3, 0.1], 'String', info_text, ...
    'EdgeColor', 'none', 'HorizontalAlignment', 'left', 'FontSize', 10);

% 创建柱状图子图
subplot(1, 2, 2);

% 准备数据
num_groups = num_corners;
group_width = 0.8;  % 每组的总宽度
bar_width = group_width / 2;  % 每个柱形的宽度

% 创建X轴位置
x_pos = 1:num_groups;

% 创建分组柱状图 - 使用手动方式绘制以控制间距
hold on;
for i = 1:num_groups
    % 机床实际路径柱形 - 红色
    bar(x_pos(i)-bar_width/2, min_machine_distances(i), bar_width, 'FaceColor', 'r');
    
    % B样条光顺路径柱形 - 绿色
    bar(x_pos(i)+bar_width/2, min_smooth_distances(i), bar_width, 'FaceColor', 'g');
end
hold off;

% 添加标题和标签
title('各拐角点的最小距离对比', 'FontSize', 25);
xlabel('拐角点', 'FontSize', 20);
ylabel('最小距离', 'FontSize', 20);

% 设置X轴刻度位置和标签
set(gca, 'XTick', x_pos);
set(gca, 'XTickLabel', {'拐角1', '拐角2', '拐角3', '拐角4', '拐角5', '拐角6', '拐角7', '拐角8', '拐角9', '拐角10'});
xlim([0.5, num_groups+0.5]);  % 设置X轴范围，确保显示完整

% 添加图例
legend({'机床实际路径', 'B样条光顺路径'}, 'Location', 'best');
grid on;

% 在每个拐角上方添加数值标签
for i = 1:num_groups
    % 机床实际路径数值
    text(x_pos(i)-bar_width/2, min_machine_distances(i)+0.01, sprintf('%.3f', min_machine_distances(i)), ...
        'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 15);
    
    % B样条光顺路径数值
    text(x_pos(i)+bar_width/2, min_smooth_distances(i)+0.01, sprintf('%.3f', min_smooth_distances(i)), ...
        'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 15);
end

% 保存图像
output_filename = ['路径对比_', strrep(filename_machine, '.txt', ''), '_vs_', strrep(filename_gcode, '.txt', ''), '.png'];
saveas(gcf, output_filename);
fprintf('对比图已保存为%s\n', output_filename);

end

function [ctrls_pos, D] = localSmoothing(pe, mcs, Length, theta, i)
% 计算拐角处的控制点和光顺参数D
l1 = Length(1,i);
l2 = Length(1,i+1);
lim = 4*pe/(3*cos(0.5*theta(1,i)));
lp = min(lim, 0.2*min(l1, l2));
D = 2.5 * lp;

% 控制点求取，其中i用于记录第几组控制点
p1 = mcs(:, i);
p2 = mcs(:, i+1);
p3 = mcs(:, i+2);
v1 = p1 - p2; % 方向向量
v2 = p3 - p2;
L1 = sqrt(v1' * v1); % 直线刀具路径的长度
L2 = sqrt(v2' * v2);
m1 = v1 / L1; % 单位向量
m2 = v2 / L2;
ctrls_pos(:, 4) = p2;
ctrls_pos(:, 3) = p2 + m1 * lp;
ctrls_pos(:, 5) = p2 + m2 * lp;
ctrls_pos(:, 2) = ctrls_pos(:, 3)*2 - p2;
ctrls_pos(:, 1) = 0.5 * (ctrls_pos(:, 3)*5 - p2*3);
ctrls_pos(:, 6) = ctrls_pos(:, 5)*2 - p2;
ctrls_pos(:, 7) = 0.5 * (ctrls_pos(:, 5)*5 - p2*3);
end

function smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector)
% 计算B样条曲线上的一系列点
% 参数:
%   ctrls_pos: 控制点坐标，每列是一个控制点的[x;y]坐标
%   k: B样条次数
%   NodeVector: 节点矢量

% 设置采样点数量
num_samples = 100;
u_values = linspace(0, 1, num_samples);

% 初始化结果数组
smooth_points = zeros(2, num_samples);

% 计算每个参数值对应的点
for idx = 1:num_samples
    u = u_values(idx);
    smooth_points(:, idx) = computeBsplinePoint(ctrls_pos, u, k, NodeVector);
end
end

function p = computeBsplinePoint(ctrls_pos, u, k, NodeVector)
% 计算B样条上参数u对应的点
n = size(ctrls_pos, 2) - 1;
Nik = zeros(n+1, 1);

for i1 = 0:n
    Nik(i1+1, 1) = BaseFunction(i1+1, k, u, NodeVector);
end

p = ctrls_pos * Nik; % 计算样条上的点
end

function val = BaseFunction(i, k, u, NodeVector)
% B样条基函数
% i为控制点序号；k为B样条次数；u为样条参数；NodeVector为节点矢量
if k==0    
    if abs(u-NodeVector(end))<10^-6
        if NodeVector(i)<=u && abs(u-NodeVector(i+1))<10^-6
            val=1;return;
        else
            val=0;return;
        end
    else
        if(u<NodeVector(i)||u>=NodeVector(i+1))
            val=0;return;
        else
            val=1;return;
        end
    end
end

if k>0
    if(u<NodeVector(i)||u>NodeVector(i+k+1)) 
        val=0;
    else
        dtemp=NodeVector(i+k)-NodeVector(i);
        if(dtemp==0) 
            alpha=0;
        else
            alpha=(u-NodeVector(i))/dtemp;
        end
        dtemp=NodeVector(i+k+1)-NodeVector(i+1);
        if(dtemp==0) 
            beta=0;
        else
            beta=(NodeVector(i+k+1)-u)/dtemp;
        end
        val1=alpha*BaseFunction(i, k-1, u, NodeVector);
        val2=beta*BaseFunction(i+1, k-1, u, NodeVector);
        val=val1+val2;
    end
end
end 