% 光顺路径，并生成作业路径点
function Interpolation()
%% 以下是脚本文件的输入
disp('-----小程序基于样条拟合精度生成最终光顺后的采样点，数控系统插补器需要额外进行插补运算-----');
disp('注意：本程序会在计算D值后减小20%再输出，以减小光顺范围，但内部计算逻辑保持不变');

% 提示用户输入路径光顺的最大逼近误差
disp(' ');
pe = input('请输入路径光顺的最大逼近误差 ( 默认值 0.3 )：');
if isempty(pe)  % 如果用户直接按回车，使用默认值
    pe = 0.3;
end

% 提示用户选择文件
disp(' ');
disp('请选择包含路径数据的文件（.txt 格式）：');

% 弹出文件选择对话框，允许用户选择文件
[filename_load, pathname_load] = uigetfile('*.txt', '选择路径文件');

% 如果用户没有取消文件选择
if filename_load ~= 0
    % 获取文件的完整路径
    fullPath = fullfile(pathname_load, filename_load);

    % 打开文件
    fileID = fopen(fullPath, 'r');
    if fileID == -1
        disp('无法打开文件');
        return;
    end

    % 初始化 points 数组
    points = [];

    % 按行读取文件内容，并解析坐标数据
    while ~feof(fileID)
        % 读取一行数据
        line = fgetl(fileID);
        if ischar(line) % 确保读取到的是有效行
            % 使用正则表达式提取X和Y的数值
            tokens = regexp(line, 'X([-\d.]+)\s+Y([-\d.]+)', 'tokens');
            
            if ~isempty(tokens)
                % 将提取的字符串转换为数值
                x = str2double(tokens{1}{1});
                y = str2double(tokens{1}{2});
                
                % 将坐标添加到 points 数组中
                points = [points; x, y];
            end
        end
    end

    % 关闭文件
    fclose(fileID);

    % 检查 points 是否成功读取
    if isempty(points)
        disp('文件中没有有效坐标数据');
    else
        disp('文件读取成功');
        disp('提取的坐标点:');
        disp(points);
    end
else
    disp('用户取消了文件选择');
end


%% 以下是脚本文件的功能函数
points = points';
num_points = size(points, 2);
num = num_points - 2; % 过渡拐角数量
Gcode = {}; % 使用 cell 数组存储 G 代码，提高效率

% 计算路径段长度
Length = sqrt(diff(points(1,:)).^2 + diff(points(2,:)).^2);

% 计算拐角处的夹角
angle = zeros(1, num);
for i = 1:num
    v1 = points(:, i) - points(:, i+1);
    v2 = points(:, i+2) - points(:, i+1);
    
    % 计算两个向量之间的夹角（弧度）
    cos_angle = (v1' * v2) / (norm(v1) * norm(v2));
    % 确保cos_angle在[-1, 1]范围内，避免数值误差
    cos_angle = max(min(cos_angle, 1), -1);
    angle(i) = acos(cos_angle);
end

% 将弧度转换为角度
angle_degrees = angle * 180 / pi;

% 打印每个拐角的角度，用于调试
disp('拐角角度（度）:');
disp(angle_degrees);

% 标记需要光顺的拐角
needs_smoothing = angle_degrees <= 170;

% 初始化G代码行号
line_index = 0;

% 添加第一个点
line_index = line_index + 1;
Gcode{end+1} = sprintf('N%d  G01  X%.6f  Y%.6f', line_index * 10, points(1, 1), points(2, 1));

% 找出所有需要光顺的连续段
smooth_segments = []; % 存储需要光顺的段落 [start_idx, end_idx]
in_smooth_section = false;
smooth_start = 0;

% 预先分析需要光顺的段落
for i = 1:num
    if needs_smoothing(i)
        if ~in_smooth_section
            smooth_start = i;
            in_smooth_section = true;
        end
    else
        if in_smooth_section
            smooth_segments = [smooth_segments; smooth_start, i-1];
            in_smooth_section = false;
        end
    end
end

% 处理最后一个光顺段
if in_smooth_section
    smooth_segments = [smooth_segments; smooth_start, num];
end

% 记录已经添加过G50的位置，避免重复添加
g50_added_at = zeros(1, num_points);

% 按顺序添加所有点和光顺命令
for i = 2:num_points
    % 检查当前点是否是光顺段的开始点
    is_smooth_start = false;
    for j = 1:size(smooth_segments, 1)
        if i == smooth_segments(j, 1) + 1 % +1 因为smooth_segments存储的是拐角索引，而不是点索引
            % 在当前点添加G51
            line_index = line_index + 1;
            Gcode{end+1} = sprintf('N%d  G51', line_index * 10);
            is_smooth_start = true;
            break;
        end
    end
    
    % 添加当前点的G代码
    line_index = line_index + 1;
    
    % 检查当前点是否在光顺段内
    is_in_smooth = false;
    for j = 1:size(smooth_segments, 1)
        if i >= smooth_segments(j, 1) + 1 && i <= smooth_segments(j, 2) + 1
            is_in_smooth = true;
            % 如果是光顺段内的点，添加D参数
            [~, D] = localSmoothing(pe, points, Length, angle, i-1);
            % 将D值减小20%再输出
            D_reduced = D * 0.8;
            Gcode{end+1} = sprintf('N%d  G01  X%.6f  Y%.6f  D%.6f', line_index * 10, points(1, i), points(2, i), D_reduced);
            break;
        end
    end
    
    % 如果不在光顺段内
    if ~is_in_smooth
        Gcode{end+1} = sprintf('N%d  G01  X%.6f  Y%.6f', line_index * 10, points(1, i), points(2, i));
    end
    
    % 检查当前点是否是光顺段的结束点的下一个点
    for j = 1:size(smooth_segments, 1)
        if i == smooth_segments(j, 2) + 2 % +2 因为我们要在结束点的下一个点添加G50
            % 在当前点添加G50，并记录已添加
            line_index = line_index + 1;
            Gcode{end+1} = sprintf('N%d  G50', line_index * 10);
            g50_added_at(i) = 1;
            break;
        end
    end
end

% 特殊情况：如果最后一个光顺段结束点是路径的倒数第二个点，需要在最后添加G50
% 但要确保之前没有添加过G50
if ~isempty(smooth_segments) && smooth_segments(end, 2) == num && num_points == num + 2 && g50_added_at(num_points) == 0
    line_index = line_index + 1;
    Gcode{end+1} = sprintf('N%d  G50', line_index * 10);
end

%% 以下是脚本文件的输出
% 提示用户保存采样点数据
disp(' ');
disp('请选择采样点数据的保存路径：');
[filename_save, pathname_save] = uiputfile('*.txt', '保存文件');
if filename_save ~= 0  % 用户没有取消选择
    fullPath = fullfile(pathname_save, filename_save);
    fid = fopen(fullPath, 'wt');
    if fid == -1
        error('无法打开文件进行写入');
    end
    % 统一写入G代码
    fprintf(fid, '%s\n', Gcode{:});
    fclose(fid);
    disp(['数据已成功保存到 ', fullPath]);
    disp('注意：输出的D值已减小20%，但内部计算保持不变');
else
    disp('用户取消了文件保存');
end
end

% function u_values = refineSampling(u_values, ctrls_pos, delta, k, NodeVector)
% % 递归细分 B 样条参数区间
% i = 1;
% while i < length(u_values)
%     u1 = u_values(i);
%     u2 = u_values(i+1);
%     u_mid = (u1 + u2) / 2;
% 
%     % 计算 u1, u2, u_mid 对应的 B 样条点
%     p1 = computeBsplinePoint(ctrls_pos, u1, k, NodeVector);
%     p2 = computeBsplinePoint(ctrls_pos, u2, k, NodeVector);
%     p_mid = computeBsplinePoint(ctrls_pos, u_mid, k, NodeVector);
% 
%     % 计算 u1 到 u2 的连线的中点
%     p_line_mid = (p1 + p2) / 2;
% 
%     % 计算偏差
%     deviation = norm(p_mid - p_line_mid);
% 
%     % 如果偏差大于精度要求，则插入新的采样点 u_mid
%     if deviation > delta
%         u_values = [u_values(1:i), u_mid, u_values(i+1:end)];
%     else
%         i = i + 1; % 进入下一个区间
%     end
% end
% end

% function p = computeBsplinePoint(ctrls_pos, u, k, NodeVector)
% % 计算 B 样条上参数 u 对应的点
% n = size(ctrls_pos, 2) - 1;
% Nik = zeros(n+1, 1);
% for i1 = 1:n+1
%     Nik(i1, 1) = BaseFunction(i1, k, u, NodeVector);
% end
% p = ctrls_pos * Nik; % 计算样条上的点
% end

function [ctrls_pos, D] = localSmoothing(pe, mcs, Length, theta, i)
l1 = Length(1,i);
l2 = Length(1,i+1);
lim = 4*pe/(3*cos(0.5*theta(1,i)));
lp = min(lim, 0.2*min(l1, l2));
D = 2.5 * lp;
% 控制点求取，其中i用于记录第几组控制点
p1 = mcs(:, i);
p2 = mcs(:, i+1);
p3 = mcs(:, i+2);
v1 = p1 - p2; % 方向向量
v2 = p3 - p2;
L1 = sqrt(v1' * v1); % 直线刀具路径的长度
L2 = sqrt(v2' * v2);
m1 = v1 / L1; % 单位向量
m2 = v2 / L2;
ctrls_pos(:, 4) = p2;
ctrls_pos(:, 3) = p2 + m1 * lp;
ctrls_pos(:, 5) = p2 + m2 * lp;
ctrls_pos(:, 2) = ctrls_pos(:, 3)*2 - p2;
ctrls_pos(:, 1) = 0.5 * (ctrls_pos(:, 3)*5 - p2*3);
ctrls_pos(:, 6) = ctrls_pos(:, 5)*2 - p2;
ctrls_pos(:, 7) = 0.5 * (ctrls_pos(:, 5)*5 - p2*3);
end

% mcc -m Interpolation.m -d Path_Smoothing_Softer