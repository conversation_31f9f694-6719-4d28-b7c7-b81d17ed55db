% 增强型光顺路径插补生成，支持圆弧指令
function EnhancedInterpolation()
%% 以下是脚本文件的输入
disp('-----增强型光顺路径生成器：支持直线和圆弧指令的光顺路径生成-----');
disp('基于样条拟合精度生成最终光顺后的采样点，支持G01/G02/G03指令');
disp('注意：本程序会对直线-直线连接的D值减小20%，对直线-圆弧、圆弧-直线和圆弧-圆弧连接的D值减小25%再输出，以减小光顺范围');

% 提示用户输入路径光顺的最大逼近误差
disp(' ');
pe = input('请输入路径光顺的最大逼近误差 (默认值 0.03)：');
if isempty(pe)  % 如果用户直接按回车，使用默认值
    pe = 0.03;
end

% 提示用户选择文件
disp(' ');
disp('请选择包含路径数据的文件（.txt 格式，G代码文件）：');

% 弹出文件选择对话框，允许用户选择文件
[filename_load, pathname_load] = uigetfile('*.txt', '选择路径文件');

% 如果用户没有取消文件选择
if filename_load ~= 0
    % 获取文件的完整路径
    fullPath = fullfile(pathname_load, filename_load);

    % 打开文件
    fileID = fopen(fullPath, 'r');
    if fileID == -1
        disp('无法打开文件');
        return;
    end

    % 初始化路径段数组
    segments = [];
    segment_types = []; % 1表示直线段，2表示圆弧段
    current_point = [0, 0]; % 当前点坐标

    % 按行读取文件内容，并解析G代码
    while ~feof(fileID)
        % 读取一行数据
        line = fgetl(fileID);
        if ~ischar(line) 
            continue;
        end
        
        % 去除前后空格
        line = strtrim(line);
        if isempty(line)
            continue;
        end
        
        % 检测G代码类型
        is_g01 = ~isempty(regexp(line, 'G0*1', 'once'));
        is_g02 = ~isempty(regexp(line, 'G0*2', 'once'));
        is_g03 = ~isempty(regexp(line, 'G0*3', 'once'));
        
        % 提取坐标
        x_match = regexp(line, 'X([-\d.]+)', 'tokens');
        y_match = regexp(line, 'Y([-\d.]+)', 'tokens');
        
        % 提取圆弧参数
        r_match = regexp(line, 'R([-\d.]+)', 'tokens');
        i_match = regexp(line, 'I([-\d.]+)', 'tokens');
        j_match = regexp(line, 'J([-\d.]+)', 'tokens');
        
        if ~isempty(x_match) && ~isempty(y_match)
            x = str2double(x_match{1}{1});
            y = str2double(y_match{1}{1});
            
            % 创建新的路径段 - 预先定义所有可能的字段以确保结构体一致性
            new_segment = struct('type', 0, ...
                                 'start_point', current_point, ...
                                 'end_point', [x, y], ...
                                 'direction', 0, ...
                                 'radius', 0, ...
                                 'center', [0, 0], ...
                                 'start_angle', 0, ...
                                 'end_angle', 0, ...
                                 'sweep_angle', 0);
            
            if is_g01 % 直线段
                new_segment.type = 1; % 直线类型
                
            elseif is_g02 || is_g03 % 圆弧段
                new_segment.type = 2; % 圆弧类型
                new_segment.direction = is_g02 + 2 * is_g03; % 1表示顺时针(G02)，2表示逆时针(G03)
                
                % 确定圆心
                if ~isempty(r_match) % 使用半径R格式
                    r = str2double(r_match{1}{1});
                    new_segment.radius = abs(r);
                    
                    % 计算圆心
                    [center_x, center_y] = calculateArcCenter(current_point(1), current_point(2), x, y, r, new_segment.direction);
                    new_segment.center = [center_x, center_y];
                    
                elseif ~isempty(i_match) && ~isempty(j_match) % 使用I,J格式
                    i_val = str2double(i_match{1}{1});
                    j_val = str2double(j_match{1}{1});
                    
                    % 圆心是相对于起点的偏移
                    center_x = current_point(1) + i_val;
                    center_y = current_point(2) + j_val;
                    new_segment.center = [center_x, center_y];
                    
                    % 计算半径
                    new_segment.radius = sqrt((center_x - current_point(1))^2 + (center_y - current_point(2))^2);
                end
                
                % 计算起点和终点相对于圆心的角度
                start_angle = atan2(current_point(2) - new_segment.center(2), current_point(1) - new_segment.center(1));
                end_angle = atan2(y - new_segment.center(2), x - new_segment.center(1));
                
                % 确保角度在正确的范围内
                if new_segment.direction == 1 % 顺时针
                    if end_angle > start_angle
                        end_angle = end_angle - 2*pi;
                    end
                else % 逆时针
                    if end_angle < start_angle
                        end_angle = end_angle + 2*pi;
                    end
                end
                
                new_segment.start_angle = start_angle;
                new_segment.end_angle = end_angle;
                new_segment.sweep_angle = abs(end_angle - start_angle);
            end
            
            % 添加新段到数组
            if isempty(segments)
                segments = new_segment;
            else
                segments(end+1) = new_segment;
            end
            
            segment_types = [segment_types; new_segment.type];
            
            % 更新当前点
            current_point = [x, y];
        end
    end

    % 关闭文件
    fclose(fileID);

    % 检查是否成功读取路径段
    if isempty(segments)
        disp('文件中没有有效的路径段数据');
        return;
    else
        disp('文件读取成功');
        disp(['提取的路径段数量: ', num2str(length(segments))]);
        disp(['直线段数量: ', num2str(sum(segment_types == 1))]);
        disp(['圆弧段数量: ', num2str(sum(segment_types == 2))]);
    end
    
    %% 找出所有连接点并确定光顺类型
    connection_points = [];
    connection_types = []; % 1=直线-直线, 2=直线-圆弧, 3=圆弧-直线, 4=圆弧-圆弧

    for i = 1:length(segments)-1
        % 这是一个连接点
        connection_point = [i, segments(i).end_point];
        
        % 确定连接类型
        if segments(i).type == 1 && segments(i+1).type == 1
            conn_type = 1; % 直线-直线
        elseif segments(i).type == 1 && segments(i+1).type == 2
            conn_type = 2; % 直线-圆弧
        elseif segments(i).type == 2 && segments(i+1).type == 1
            conn_type = 3; % 圆弧-直线
        else % segments(i).type == 2 && segments(i+1).type == 2
            conn_type = 4; % 圆弧-圆弧
        end
        
        % 如果是第一个连接点，直接赋值
        if isempty(connection_points)
            connection_points = connection_point;
            connection_types = conn_type;
        else
            % 否则添加到现有数组
            connection_points(end+1,:) = connection_point;
            connection_types(end+1) = conn_type;
        end
    end

    disp(['找到的连接点数量: ', num2str(size(connection_points, 1))]);
    disp('连接点类型统计:');
    disp(['直线-直线连接: ', num2str(sum(connection_types == 1))]);
    disp(['直线-圆弧连接: ', num2str(sum(connection_types == 2))]);
    disp(['圆弧-直线连接: ', num2str(sum(connection_types == 3))]);
    disp(['圆弧-圆弧连接: ', num2str(sum(connection_types == 4))]);
    
    %% 生成光顺G代码
    Gcode = {}; % 使用 cell 数组存储 G 代码
    line_index = 0;
    
    % 如果有多个段，则直接从第一个段开始，不添加额外的起点移动指令
    % 记录需要光顺的连接点和类型
    smooth_segments = connection_points(:, 1);
    smooth_types = connection_types;
    
    % 预先计算所有光顺段的D值
    d_values = zeros(1, length(segments));
    
    % 计算每个光顺连接处的D值
    for i = 1:size(connection_points, 1)
        segment_idx = connection_points(i, 1);
        conn_type = connection_types(i);
        
        % 根据连接类型计算D值
        if conn_type == 1 % 直线-直线
            [ctrls_pos, D] = lineLineSmoothing(pe, segments(segment_idx), segments(segment_idx + 1));
            % 计算前一段和后一段的实际D值（从控制点到拐角的距离）
            p_i = segments(segment_idx).end_point(:);  % 拐角点
            d0 = ctrls_pos(:, 1);  % 第一个控制点
            d6 = ctrls_pos(:, 7);  % 最后一个控制点
            d_values(segment_idx) = norm(p_i - d0);     % 前一段的D值
            d_values(segment_idx+1) = norm(p_i - d6);   % 后一段的D值
            
            % 直线-直线连接的D值减小20%
            d_values(segment_idx) = d_values(segment_idx) * 0.8;
            d_values(segment_idx+1) = d_values(segment_idx+1) * 0.8;
            
        elseif conn_type == 2 % 直线-圆弧
            [ctrls_pos, optimal_lp, optimal_theta_e] = lineArcSymmetricSmoothing(pe, segments(segment_idx), segments(segment_idx + 1));
            % 计算直线侧和圆弧侧的D值
            p_i = segments(segment_idx).end_point(:);  % 拐角点
            d0 = ctrls_pos(:, 1);  % 直线侧第一个控制点
            d6 = ctrls_pos(:, 7);  % 圆弧侧最后一个控制点
            d_values(segment_idx) = norm(p_i - d0);     % 直线段的D值
            d_values(segment_idx+1) = norm(p_i - d6);   % 圆弧段的D值
            
            % 直线-圆弧连接的D值减小25%
            d_values(segment_idx) = d_values(segment_idx) * 0.75;
            d_values(segment_idx+1) = d_values(segment_idx+1) * 0.75;
            
        elseif conn_type == 3 % 圆弧-直线
            [ctrls_pos, optimal_theta_e, optimal_lp] = arcLineSymmetricSmoothing(pe, segments(segment_idx), segments(segment_idx + 1));
            % 计算圆弧侧和直线侧的D值
            p_i = segments(segment_idx).end_point(:);  % 拐角点
            d0 = ctrls_pos(:, 1);  % 圆弧侧第一个控制点
            d6 = ctrls_pos(:, 7);  % 直线侧最后一个控制点
            d_values(segment_idx) = norm(p_i - d0);     % 圆弧段的D值
            d_values(segment_idx+1) = norm(p_i - d6);   % 直线段的D值
            
            % 圆弧-直线连接的D值减小25%
            d_values(segment_idx) = d_values(segment_idx) * 0.75;
            d_values(segment_idx+1) = d_values(segment_idx+1) * 0.75;
            
        else % conn_type == 4 % 圆弧-圆弧
            [ctrls_pos, optimal_theta_s, optimal_theta_e] = arcArcSymmetricSmoothing(pe, segments(segment_idx), segments(segment_idx + 1));
            % 计算两个圆弧侧的D值
            p_i = segments(segment_idx).end_point(:);  % 拐角点
            d0 = ctrls_pos(:, 1);  % 第一个圆弧侧的第一个控制点
            d6 = ctrls_pos(:, 7);  % 第二个圆弧侧的最后一个控制点
            d_values(segment_idx) = norm(p_i - d0);     % 第一个圆弧段的D值
            d_values(segment_idx+1) = norm(p_i - d6);   % 第二个圆弧段的D值
            
            % 圆弧-圆弧连接的D值减小24%
            d_values(segment_idx) = d_values(segment_idx) * 0.76;
            d_values(segment_idx+1) = d_values(segment_idx+1) * 0.76;
        end
        
        % 显示计算的D值
        disp(['连接点 ', num2str(segment_idx), ' (', num2str(segments(segment_idx).end_point(1)), ',', num2str(segments(segment_idx).end_point(2)), ') 的D值:']);
        if conn_type == 1 % 直线-直线连接
            disp(['  前段D值: ', num2str(d_values(segment_idx)), ' (已减小20%)']);
            disp(['  后段D值: ', num2str(d_values(segment_idx+1)), ' (已减小20%)']);
        elseif conn_type == 4 % 圆弧-圆弧连接
            disp(['  前段D值: ', num2str(d_values(segment_idx)), ' (已减小24%)']);
            disp(['  后段D值: ', num2str(d_values(segment_idx+1)), ' (已减小24%)']);
        else
            disp(['  前段D值: ', num2str(d_values(segment_idx)), ' (已减小25%)']);
            disp(['  后段D值: ', num2str(d_values(segment_idx+1)), ' (已减小25%)']);
        end
    end
    
    % 记录光顺状态
    smoothing_active = false;
    
    % 对每个段进行处理
    for i = 1:length(segments)
        current_segment = segments(i);
        
        % 检查当前段的起点是否需要开始光顺
        if i > 1 && ismember(i-1, smooth_segments) && ~smoothing_active
            % 添加G51指令（开始光顺）
            line_index = line_index + 1;
            Gcode{end+1} = sprintf('N%d G51', line_index * 10);
            smoothing_active = true; % 标记光顺已开启
        end
        
        % 添加当前段的G代码
        line_index = line_index + 1;
        
        % 检查当前段是否在光顺过程中
        is_in_smooth = smoothing_active;
        
        if current_segment.type == 1  % 直线段
            if is_in_smooth
                Gcode{end+1} = sprintf('N%d G01 X%.6f Y%.6f D%.6f', line_index * 10, current_segment.end_point(1), current_segment.end_point(2), d_values(i));
            else
                Gcode{end+1} = sprintf('N%d G01 X%.6f Y%.6f', line_index * 10, current_segment.end_point(1), current_segment.end_point(2));
            end
        else  % 圆弧段
            % 确定圆弧命令类型
            g_cmd = 'G02';
            if current_segment.direction == 2  % 逆时针
                g_cmd = 'G03';
            end
            
            % 计算圆心相对于起点的偏移
            i_val = current_segment.center(1) - current_segment.start_point(1);
            j_val = current_segment.center(2) - current_segment.start_point(2);
            
            if is_in_smooth
                % 在光顺状态下，保留原始的R或IJ值，并添加D值
                % 检查是否有R值
                if isfield(current_segment, 'radius') && current_segment.radius > 0
                    Gcode{end+1} = sprintf('N%d %s X%.6f Y%.6f R%.6f D%.6f', line_index * 10, g_cmd, current_segment.end_point(1), current_segment.end_point(2), current_segment.radius, d_values(i));
                else
                    Gcode{end+1} = sprintf('N%d %s X%.6f Y%.6f I%.6f J%.6f D%.6f', line_index * 10, g_cmd, current_segment.end_point(1), current_segment.end_point(2), i_val, j_val, d_values(i));
                end
            else
                % 非光顺状态下，保持原样
                if isfield(current_segment, 'radius') && current_segment.radius > 0
                    Gcode{end+1} = sprintf('N%d %s X%.6f Y%.6f R%.6f', line_index * 10, g_cmd, current_segment.end_point(1), current_segment.end_point(2), current_segment.radius);
                else
                    Gcode{end+1} = sprintf('N%d %s X%.6f Y%.6f I%.6f J%.6f', line_index * 10, g_cmd, current_segment.end_point(1), current_segment.end_point(2), i_val, j_val);
                end
            end
        end
        
        % 检查当前段是否是最后一个需要光顺的段
        is_last_smooth_segment = false;
        if i < length(segments)
            next_segment_start = i;
            is_last_smooth_segment = ismember(i, smooth_segments) && ~ismember(next_segment_start, smooth_segments);
        else
            is_last_smooth_segment = ismember(i, smooth_segments);
        end
        
        % 如果是最后一个需要光顺的段且光顺已开启，则添加G50
        if is_last_smooth_segment && smoothing_active
            line_index = line_index + 1;
            Gcode{end+1} = sprintf('N%d G50', line_index * 10);
            smoothing_active = false; % 重置光顺状态
        end
    end
    
    % 确保程序结束时光顺已关闭
    if smoothing_active
        line_index = line_index + 1;
        Gcode{end+1} = sprintf('N%d G50', line_index * 10);
    end
    
    %% 以下是脚本文件的输出
    % 提示用户保存G代码数据
    disp(' ');
    disp('请选择G代码数据的保存路径：');
    [filename_save, pathname_save] = uiputfile('*.txt', '保存文件');
    if filename_save ~= 0  % 用户没有取消选择
        fullPath = fullfile(pathname_save, filename_save);
        fid = fopen(fullPath, 'wt');
        if fid == -1
            error('无法打开文件进行写入');
        end
        % 统一写入G代码
        fprintf(fid, '%s\n', Gcode{:});
        fclose(fid);
        disp(['数据已成功保存到 ', fullPath]);
        disp('注意：直线-直线连接的D值已减小20%，直线-圆弧、圆弧-直线连接的D值已减小25%，圆弧-圆弧连接的D值已减小24%');
    else
        disp('用户取消了文件保存');
    end
else
    disp('用户取消了文件选择');
end
end

%% 圆弧中心计算函数
function [center_x, center_y] = calculateArcCenter(x1, y1, x2, y2, r, direction)
% 根据起点、终点、半径和方向计算圆弧中心点
% 输入:
%   x1, y1: 起点坐标
%   x2, y2: 终点坐标
%   r: 半径
%   direction: 1表示顺时针(G02)，2表示逆时针(G03)
% 输出:
%   center_x, center_y: 圆心坐标

% 计算起点和终点之间的中点
mid_x = (x1 + x2) / 2;
mid_y = (y1 + y2) / 2;

% 计算起点和终点之间的距离
chord = sqrt((x2 - x1)^2 + (y2 - y1)^2);

% 检查半径是否足够大
if abs(r) < chord/2
    error('半径太小，无法创建圆弧');
end

% 计算从中点到圆心的距离
h = sqrt(r^2 - (chord/2)^2);

% 计算起点到终点的向量
dx = x2 - x1;
dy = y2 - y1;

% 单位化
len = sqrt(dx^2 + dy^2);
dx = dx / len;
dy = dy / len;

% 计算垂直于起点到终点向量的单位向量
% 对于G02(顺时针)和G03(逆时针)，垂直方向相反
if direction == 1 % 顺时针(G02)
    nx = dy;   % 向右旋转90度
    ny = -dx;
else % 逆时针(G03)
    nx = -dy;  % 向左旋转90度
    ny = dx;
end

% 计算圆心
center_x = mid_x + h * nx;
center_y = mid_y + h * ny;

% 验证计算结果
r1 = sqrt((center_x - x1)^2 + (center_y - y1)^2);
r2 = sqrt((center_x - x2)^2 + (center_y - y2)^2);

% 检查计算的圆心是否满足条件
if abs(r1 - abs(r)) > 1e-6 || abs(r2 - abs(r)) > 1e-6
    warning('计算的圆心可能不准确，请检查。');
    fprintf('计算的半径: r1=%.6f, r2=%.6f, 期望半径: r=%.6f\n', r1, r2, abs(r));
    
    % 尝试调整圆心位置，使其满足半径条件
    % 计算圆心到起点和终点的单位向量
    v1 = [x1 - center_x, y1 - center_y] / r1;
    v2 = [x2 - center_x, y2 - center_y] / r2;
    
    % 调整圆心位置
    center_x = (x1 - r * v1(1) + x2 - r * v2(1)) / 2;
    center_y = (y1 - r * v1(2) + y2 - r * v2(2)) / 2;
    
    % 重新验证
    r1 = sqrt((center_x - x1)^2 + (center_y - y1)^2);
    r2 = sqrt((center_x - x2)^2 + (center_y - y2)^2);
    fprintf('调整后的半径: r1=%.6f, r2=%.6f\n', r1, r2);
end

% 验证方向是否正确
% 计算起点和终点相对于圆心的角度
angle1 = atan2(y1 - center_y, x1 - center_x);
angle2 = atan2(y2 - center_y, x2 - center_x);

% 根据方向调整角度，使其在正确的范围内
if direction == 1 % 顺时针(G02)
    if angle2 > angle1
        angle2 = angle2 - 2*pi;
    end
    sweep_angle = angle1 - angle2;
else % 逆时针(G03)
    if angle2 < angle1
        angle2 = angle2 + 2*pi;
    end
    sweep_angle = angle2 - angle1;
end

% 如果计算出的弧度小于0或大于π，可能圆心方向错误
if sweep_angle < 0 || sweep_angle > pi
    % 尝试反向计算
    center_x = mid_x - h * nx;
    center_y = mid_y - h * ny;
    
    % 重新验证
    r1 = sqrt((center_x - x1)^2 + (center_y - y1)^2);
    r2 = sqrt((center_x - x2)^2 + (center_y - y2)^2);
    
    if abs(r1 - abs(r)) > 1e-6 || abs(r2 - abs(r)) > 1e-6
        warning('反向计算的圆心也可能不准确，请检查。');
    end
end
end

%% 直线-直线光顺函数
function [ctrls_pos, D] = lineLineSmoothing(pe, line_segment1, line_segment2)
% 计算直线-直线连接处的光顺控制点
% 输入:
%   pe: 最大逼近误差
%   line_segment1: 第一条直线段
%   line_segment2: 第二条直线段
% 输出:
%   ctrls_pos: 7个控制点的坐标 [2x7]
%   D: 光顺参数D

% 获取拐角点和相邻点
p1 = line_segment1.start_point';
p2 = line_segment1.end_point'; % 拐角点
p3 = line_segment2.end_point';

% 计算方向向量
v1 = p1 - p2; % 第一条直线的方向向量
v2 = p3 - p2; % 第二条直线的方向向量

% 计算直线长度
L1 = norm(v1);
L2 = norm(v2);

% 计算单位向量
m1 = v1 / L1;
m2 = v2 / L2;

% 计算拐角角度
cos_theta = dot(m1, m2);
cos_theta = max(min(cos_theta, 1), -1); % 确保在[-1, 1]范围内
theta = acos(cos_theta);

% 计算光顺参数lp (根据误差要求和角度)
lim = 4 * pe / (3 * cos(0.5 * theta));
lp = min(lim, 0.2 * min(L1, L2));
D = 2.5 * lp;

% 计算7个控制点
ctrls_pos = zeros(2, 7);
ctrls_pos(:, 4) = p2; % 中心控制点是拐角点

% 计算其他控制点
ctrls_pos(:, 3) = p2 + m1 * lp;
ctrls_pos(:, 5) = p2 + m2 * lp;
ctrls_pos(:, 2) = ctrls_pos(:, 3) * 2 - p2;
ctrls_pos(:, 1) = 0.5 * (ctrls_pos(:, 3) * 5 - p2 * 3);
ctrls_pos(:, 6) = ctrls_pos(:, 5) * 2 - p2;
ctrls_pos(:, 7) = 0.5 * (ctrls_pos(:, 5) * 5 - p2 * 3);
end

%% 直线-圆弧光顺函数 (最终版：带边界决策的对称优化法)
function [ctrls_pos, optimal_lp, optimal_theta_e] = lineArcSymmetricSmoothing(pe, line_segment, arc_segment)
    % --- 几何信息获取 ---
    p_i = line_segment.end_point(:);
    v_line = p_i - line_segment.start_point(:);
    m_line = v_line / norm(v_line);
    center = arc_segment.center(:);
    v_arc = p_i - center;
    r = arc_segment.radius;
    if arc_segment.direction == 2
        m_arc = [-v_arc(2); v_arc(1)] / r;
    else
        m_arc = [v_arc(2); -v_arc(1)] / r;
    end
    m_in = m_line;
    m_out = m_arc;
    
    % --- G1连续性检查 ---
    if norm(m_in - m_out) < 1e-6
        disp(['在点 (', num2str(p_i(1)), ', ', num2str(p_i(2)), ') 检测到G1连续，跳过光顺。']);
        ctrls_pos = repmat(p_i, 1, 7); 
        optimal_lp = 0;
        optimal_theta_e = 0;
        return; 
    end

    % ======================= 最终算法逻辑开始 =======================

    % --- 步骤 1：计算几何允许的最大对称光顺角 theta_max_geom ---
    line_length = norm(line_segment.end_point(:) - line_segment.start_point(:));
    arc_sweep_angle = arc_segment.sweep_angle;
    % 1a. 来自圆弧侧的约束
    theta_max_from_arc = 0.5 * arc_sweep_angle;
    % 1b. 来自直线侧的约束 (通过对称关系换算)
    asin_arg = (0.5 * line_length) / (2 * r);
    if asin_arg > 1
        theta_max_from_line = pi; % 直线很长，此约束无效
    else
        theta_max_from_line = 2 * asin(asin_arg);
    end
    % 1c. 几何允许的最大光顺角
    theta_max_geom = min([pi/2, theta_max_from_arc, theta_max_from_line]);
    theta_max_geom = max(theta_max_geom, 0.001);

    % --- 步骤 2：计算满足精度pe的理想光顺角 theta_for_pe ---
    v_diff = m_out - m_in;
    v_bisector = v_diff / norm(v_diff);
    P_constraint = p_i + pe * v_bisector;
    objectiveHandle = @(theta_e) objective_symmetric_line_arc(theta_e, P_constraint, p_i, line_segment, arc_segment);
    options = optimset('TolX', 1e-8, 'Display', 'off');
    % 搜索范围可以比较宽松，因为最终结果会被theta_max_geom约束
    theta_for_pe = fminbnd(objectiveHandle, 0.001, min(pi/2, arc_sweep_angle), options);

    % --- 步骤 3：最终决策 ---
    if theta_for_pe <= theta_max_geom
        disp(['决策: 采用理想精度解, 误差 ≈ ', num2str(pe)]);
        optimal_theta_e = theta_for_pe;
    else
        disp(['决策: 理想解超限，采用几何极限解以保证不干涉。误差将小于 ', num2str(pe)]);
        optimal_theta_e = theta_max_geom;
    end
    
    % 根据最终确定的最优theta_e，计算对应的lp
    optimal_lp = 2 * r * sin(optimal_theta_e / 2);
    
    % 使用最优参数计算控制点
    ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, optimal_theta_e, optimal_lp, true, arc_segment);
end

% 目标函数 (以theta_e为变量)
function cost = objective_symmetric_line_arc(theta_e, P_constraint, p_i, line_segment, arc_segment)
    r = arc_segment.radius;
    lp = 2 * r * sin(theta_e / 2);
    
    line_length = norm(line_segment.end_point(:) - line_segment.start_point(:));
    if lp > 0.5 * line_length % 增加一个硬性检查，虽然理论上fminbnd的边界已处理
        cost = inf;
        return;
    end
    
    v_line = p_i - line_segment.start_point(:);
    m_line = v_line / norm(v_line);
    ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, theta_e, lp, true, arc_segment);
    k = 5;
    NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
    mid_point = computeBsplinePoint(ctrls_pos, 0.5, k, NodeVector);
    error_vector = mid_point - P_constraint;
    cost = sum(error_vector.^2);
end

%% 直线-圆弧光顺函数 (基于优化的直接调整法)
function [ctrls_pos, optimal_theta_e, optimal_lp] = arcLineSymmetricSmoothing(pe, arc_segment, line_segment)
    % --- 几何信息获取 ---
    p_i = arc_segment.end_point(:);
    v_line = line_segment.end_point(:) - p_i;
    m_line = v_line / norm(v_line);
    center = arc_segment.center(:);
    v_arc = p_i - center;
    r = arc_segment.radius;
    if arc_segment.direction == 2
        m_arc = [-v_arc(2); v_arc(1)] / r;
    else
        m_arc = [v_arc(2); -v_arc(1)] / r;
    end
    m_in = m_arc;
    m_out = m_line;

    % --- G1连续性检查 ---
    if norm(m_in - m_out) < 1e-6
        disp(['在点 (', num2str(p_i(1)), ', ', num2str(p_i(2)), ') 检测到G1连续，跳过光顺。']);
        ctrls_pos = repmat(p_i, 1, 7); 
        optimal_theta_e = 0;
        optimal_lp = 0;
        return; 
    end

    % ======================= 最终算法逻辑开始 =======================

    % --- 步骤 1：计算几何允许的最大对称光顺角 theta_max_geom ---
    % 计算圆弧段扫描角度和直线段长度
    arc_sweep_angle = arc_segment.sweep_angle;
    line_length = norm(line_segment.end_point(:) - p_i);

    % 1a. 来自圆弧侧的约束
    theta_max_from_arc = 0.5 * arc_sweep_angle;

    % 1b. 来自直线侧的约束（通过对称关系换算）
    asin_arg = (0.5 * line_length) / (2 * r);
    if asin_arg > 1
        theta_max_from_line = pi; % 直线很长，此约束无效
    else
        theta_max_from_line = 2 * asin(asin_arg);
    end

    % 1c. 几何允许的最大光顺角是所有约束中的最小值
    theta_max_geom = min([pi/2, theta_max_from_arc, theta_max_from_line]);
    theta_max_geom = max(theta_max_geom, 0.001); % 保证不为零

    % --- 步骤 2：计算满足精度pe的理想光顺角 theta_for_pe ---
    v_diff = m_out - m_in;
    v_bisector = v_diff / norm(v_diff);
    P_constraint = p_i + pe * v_bisector;
    objectiveHandle = @(theta_e) objective_symmetric_arc_line(theta_e, P_constraint, p_i, arc_segment, line_segment);
    options = optimset('TolX', 1e-8, 'Display', 'off');
    % 搜索范围可以比较宽松，因为最终结果会被theta_max_geom约束
    theta_for_pe = fminbnd(objectiveHandle, 0.001, min(pi/2, arc_sweep_angle), options);

    % --- 步骤 3：最终决策 ---
    if theta_for_pe <= theta_max_geom
        disp(['决策: 采用理想精度解, 误差 ≈ ', num2str(pe)]);
        optimal_theta_e = theta_for_pe;
    else
        disp(['决策: 理想解超限，采用几何极限解以保证不干涉。误差将小于 ', num2str(pe)]);
        optimal_theta_e = theta_max_geom;
    end
    
    % 根据最终确定的最优theta_e，计算对应的lp
    optimal_lp = 2 * r * sin(optimal_theta_e / 2);
    
    % 使用最优参数计算控制点
    ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, optimal_theta_e, optimal_lp, false, arc_segment);
end

function cost = objective_symmetric_arc_line(theta_e, P_constraint, p_i, arc_segment, line_segment)
    % 根据对称约束计算直线侧光顺长度
    r = arc_segment.radius;
    lp = 2 * r * sin(theta_e / 2);
    
    % 计算直线方向单位向量
    v_line = line_segment.end_point(:) - p_i(:);
    m_line = v_line / norm(v_line);
    
    % 计算控制点
    ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, theta_e, lp, false, arc_segment);
    
    % 计算B样条曲线在u=0.5处的点
    k = 5; % B样条次数
    NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
    mid_point = computeBsplinePoint(ctrls_pos, 0.5, k, NodeVector);
    
    % 计算误差向量的平方和
    error_vector = mid_point - P_constraint;
    cost = sum(error_vector.^2);
end

%% 圆弧-圆弧光顺函数 (最终版：带边界决策的对称优化法)
function [ctrls_pos, optimal_theta_s, optimal_theta_e] = arcArcSymmetricSmoothing(pe, arc_segment1, arc_segment2)
    % --- 几何信息获取 ---
    p_i = arc_segment1.end_point(:);
    center1 = arc_segment1.center(:);
    v_arc1 = p_i - center1;
    r1 = arc_segment1.radius;
    if arc_segment1.direction == 2
        m_arc1 = [-v_arc1(2); v_arc1(1)] / r1;
    else
        m_arc1 = [v_arc1(2); -v_arc1(1)] / r1;
    end
    
    center2 = arc_segment2.center(:);
    v_arc2 = p_i - center2;
    r2 = arc_segment2.radius;
    if arc_segment2.direction == 2
        m_arc2 = [-v_arc2(2); v_arc2(1)] / r2;
    else
        m_arc2 = [v_arc2(2); -v_arc2(1)] / r2;
    end
    
    m_in = m_arc1;
    m_out = m_arc2;
    
    % 计算半径比例
    r_ratio = r1 / r2;

    % --- G1连续性检查 ---
    if norm(m_in - m_out) < 1e-6
        disp(['在点 (', num2str(p_i(1)), ', ', num2str(p_i(2)), ') 检测到G1连续，跳过光顺。']);
        ctrls_pos = repmat(p_i, 1, 7); 
        optimal_theta_s = 0;
        optimal_theta_e = 0;
        return; 
    end

    % ======================= 最终算法逻辑开始 =======================

    % --- 步骤 1：计算几何允许的最大对称光顺角 theta_max_geom ---
    % 计算两条圆弧段扫描角度
    arc_sweep_angle1 = arc_segment1.sweep_angle;
    arc_sweep_angle2 = arc_segment2.sweep_angle;
    
    % 1a. 来自第一条圆弧的约束
    theta_max_from_arc1 = 0.5 * arc_sweep_angle1;
    
    % 1b. 来自第二条圆弧的约束（通过对称关系换算）
    theta_max_from_arc2 = 2 * asin(sin(0.5 * arc_sweep_angle2 / 2) / r_ratio);
    
    % 1c. 来自半径比例的arcsin约束
    if r_ratio > 1
        theta_max_by_asin = 2 * asin(1 / r_ratio);
    else
        theta_max_by_asin = pi; % 理论上最大是pi，但实际会被其他约束限制
    end
    
    % 1d. 几何允许的最大光顺角
    theta_max_geom = min([pi/2, theta_max_from_arc1, theta_max_from_arc2, theta_max_by_asin]);
    theta_max_geom = max(theta_max_geom, 0.001); % 保证不为零

    % --- 步骤 2：计算满足精度pe的理想光顺角 theta_for_pe ---
    v_diff = m_out - m_in;
    v_bisector = v_diff / norm(v_diff);
    P_constraint = p_i + pe * v_bisector;
    objectiveHandle = @(theta_s) objective_symmetric_arc_arc(theta_s, P_constraint, p_i, arc_segment1, arc_segment2, r_ratio);
    options = optimset('TolX', 1e-8, 'Display', 'off');
    % 搜索范围可以比较宽松，因为最终结果会被theta_max_geom约束
    theta_for_pe = fminbnd(objectiveHandle, 0.001, min(pi/2, arc_sweep_angle1), options);

    % --- 步骤 3：最终决策 ---
    if theta_for_pe <= theta_max_geom
        disp(['决策: 采用理想精度解, 误差 ≈ ', num2str(pe)]);
        optimal_theta_s = theta_for_pe;
    else
        disp(['决策: 理想解超限，采用几何极限解以保证不干涉。误差将小于 ', num2str(pe)]);
        optimal_theta_s = theta_max_geom;
    end
    
    % 根据最终确定的最优theta_s，计算对应的theta_e（对称约束方程）
    asin_param = r_ratio * sin(optimal_theta_s / 2);
    if abs(asin_param) > 1
        disp('警告: 对称约束方程无法求解，半径比与角度关系不兼容!');
        % 使用备用方案
        optimal_theta_e = optimal_theta_s;
    else
        optimal_theta_e = 2 * asin(asin_param);
    end
    
    % 使用最优参数计算控制点
    ctrls_pos = calculateControlPoints_ArcArc_Correct(p_i, arc_segment1, arc_segment2, optimal_theta_s, optimal_theta_e);
end

function cost = objective_symmetric_arc_arc(theta_s, P_constraint, p_i, arc_segment1, arc_segment2, r_ratio)
    % 根据对称约束计算第二圆弧侧光顺角度
    % 检查arcsin的参数是否有效
    asin_param = r_ratio * sin(theta_s / 2);
    if abs(asin_param) > 1
        % 如果参数无效，返回一个巨大的惩罚值
        cost = inf;
        return;
    end

    theta_e = 2 * asin(asin_param);

    % 计算控制点
    ctrls_pos = calculateControlPoints_ArcArc_Correct(p_i, arc_segment1, arc_segment2, theta_s, theta_e);

    % 计算B样条曲线在u=0.5处的点
    k = 5; % B样条次数
    NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
    mid_point = computeBsplinePoint(ctrls_pos, 0.5, k, NodeVector);

    % 计算误差向量的平方和
    error_vector = mid_point - P_constraint;
    cost = sum(error_vector.^2);
end

%% 直线-圆弧控制点计算函数
function ctrls_pos = calculateControlPoints_LineArc(p_i, m_line, r, theta_e, lp_s, line_to_arc, arc_segment)
% 计算直线-圆弧或圆弧-直线连接处的B样条控制点
% 输入:
%   p_i: 连接点坐标
%   m_line: 直线方向单位向量
%   r: 圆弧半径
%   theta_e: 光顺角度
%   lp_s: 直线侧光顺长度
%   line_to_arc: 布尔值，true表示直线-圆弧，false表示圆弧-直线
%   arc_segment: 圆弧段
% 输出:
%   ctrls_pos: 7个控制点的坐标 [2x7]

% 确保输入向量是列向量
p_i = p_i(:);
m_line = m_line(:);

% 初始化
ctrls_pos = zeros(2, 7);
d3 = p_i;
ctrls_pos(:, 4) = d3;
center = arc_segment.center(:); % 确保是列向量
arc_direction = arc_segment.direction;
c_l1 = 0.35; c_l2 = 0.45; c_l3 = 0.2;

if line_to_arc
    %% CASE 1: 直线 -> 圆弧
    % 1. 直线侧控制点 d0, d1, d2
    ctrls_pos(:, 3) = d3 - c_l1 * lp_s * m_line;
    ctrls_pos(:, 2) = ctrls_pos(:, 3) - c_l2 * lp_s * m_line;
    ctrls_pos(:, 1) = ctrls_pos(:, 2) - c_l3 * lp_s * m_line;

    % 2. 圆弧侧控制点 d4, d5, d6
    angle_at_d3 = atan2(p_i(2) - center(2), p_i(1) - center(1));
    if arc_direction == 2 % 逆时针 G03
        angle_at_d6 = angle_at_d3 + theta_e;
    else % 顺时针 G02
        angle_at_d6 = angle_at_d3 - theta_e;
    end
    d6 = center + r * [cos(angle_at_d6); sin(angle_at_d6)];
    
    if arc_direction == 2 % 逆时针 G03
        m_e = [-sin(angle_at_d6); cos(angle_at_d6)];
    else % 顺时针 G02
        m_e = [sin(angle_at_d6); -cos(angle_at_d6)];
    end

    vec_d6_c = d6 - center;
    d5 = d6 - (0.4 * r * sin(theta_e)) * m_e;
    d4 = (0.8 + 0.2*cos(theta_e))*vec_d6_c - (0.57*r*sin(theta_e))*m_e + center;
    
    ctrls_pos(:, 7) = d6;
    ctrls_pos(:, 6) = d5;
    ctrls_pos(:, 5) = d4;

else
    %% CASE 2: 圆弧 -> 直线
    % 1. 直线侧控制点 d4, d5, d6
    ctrls_pos(:, 5) = d3 + c_l1 * lp_s * m_line;
    ctrls_pos(:, 6) = ctrls_pos(:, 5) + c_l2 * lp_s * m_line;
    ctrls_pos(:, 7) = ctrls_pos(:, 6) + c_l3 * lp_s * m_line;

    % 2. 圆弧侧控制点 d0, d1, d2
    angle_at_d3 = atan2(p_i(2) - center(2), p_i(1) - center(1));
    if arc_direction == 2 % 逆时针 G03
        angle_at_d0 = angle_at_d3 - theta_e;
    else % 顺时针 G02
        angle_at_d0 = angle_at_d3 + theta_e;
    end
    d0 = center + r * [cos(angle_at_d0); sin(angle_at_d0)];
    
    if arc_direction == 2 % 逆时针 G03
        m_s = [-sin(angle_at_d0); cos(angle_at_d0)];
    else % 顺时针 G02
        m_s = [sin(angle_at_d0); -cos(angle_at_d0)];
    end

    vec_d0_c = d0 - center;
    d1 = d0 + (0.4 * r * sin(theta_e)) * m_s;
    d2 = (0.8 + 0.2*cos(theta_e))*vec_d0_c + (0.57*r*sin(theta_e))*m_s + center;
    
    ctrls_pos(:, 1) = d0;
    ctrls_pos(:, 2) = d1;
    ctrls_pos(:, 3) = d2;
end
end

%% 正确的圆弧-圆弧控制点计算函数
function ctrls_pos = calculateControlPoints_ArcArc_Correct(p_i, arc1, arc2, theta_s, theta_e)
% 根据第一篇论文的公式，计算圆弧-圆弧光顺的7个五次B样条控制点
% p_i: 角隅点坐标 (列向量)
% arc1, arc2: 两个圆弧段结构体
% theta_s, theta_e: 两侧的光顺角度

% 初始化控制点数组 (2x7)
ctrls_pos = zeros(2, 7);
% 中心控制点 d3 即为角隅点 p_i
ctrls_pos(:, 4) = p_i(:); % 确保是列向量

%% 计算第一段圆弧侧(s-side)的控制点 d0, d1, d2
center_s = arc1.center(:);
angle_at_d3_s = atan2(p_i(2) - center_s(2), p_i(1) - center_s(1));

% 根据圆弧方向回退角度，找到光顺段起点d0
if arc1.direction == 2 % 逆时针 G03
    angle_at_d0 = angle_at_d3_s - theta_s;
else % 顺时针 G02
    angle_at_d0 = angle_at_d3_s + theta_s;
end
d0 = center_s + arc1.radius * [cos(angle_at_d0); sin(angle_at_d0)];

% 计算 d0 处的切线向量 m_s
vec_d0_c = d0 - center_s;
if arc1.direction == 2 % 逆时针 G03
    m_s = [-vec_d0_c(2); vec_d0_c(1)] / norm(vec_d0_c);
else % 顺时针 G02
    m_s = [vec_d0_c(2); -vec_d0_c(1)] / norm(vec_d0_c);
end

% 根据论文公式(10)的原理计算 d1 和 d2
d1 = d0 + (0.4 * arc1.radius * sin(theta_s)) * m_s;
d2 = (0.8 + 0.2*cos(theta_s))*vec_d0_c + (0.57 * arc1.radius * sin(theta_s))*m_s + center_s;

ctrls_pos(:, 1) = d0;
ctrls_pos(:, 2) = d1;
ctrls_pos(:, 3) = d2;

%% 计算第二段圆弧侧(e-side)的控制点 d4, d5, d6
center_e = arc2.center(:);
angle_at_d3_e = atan2(p_i(2) - center_e(2), p_i(1) - center_e(1));

% 根据圆弧方向前进角度，找到光顺段终点d6
if arc2.direction == 2 % 逆时针 G03
    angle_at_d6 = angle_at_d3_e + theta_e;
else % 顺时针 G02
    angle_at_d6 = angle_at_d3_e - theta_e;
end
d6 = center_e + arc2.radius * [cos(angle_at_d6); sin(angle_at_d6)];

% 计算 d6 处的切线向量 m_e
vec_d6_c = d6 - center_e;
if arc2.direction == 2 % 逆时针 G03
    m_e = [-vec_d6_c(2); vec_d6_c(1)] / norm(vec_d6_c);
else % 顺时针 G02
    m_e = [vec_d6_c(2); -vec_d6_c(1)] / norm(vec_d6_c);
end

% 根据论文公式(9)的原理计算 d4 和 d5
d5 = d6 - (0.4 * arc2.radius * sin(theta_e)) * m_e;
d4 = (0.8 + 0.2*cos(theta_e))*vec_d6_c - (0.57 * arc2.radius * sin(theta_e))*m_e + center_e;

ctrls_pos(:, 7) = d6;
ctrls_pos(:, 6) = d5;
ctrls_pos(:, 5) = d4;
end

%% B样条曲线计算函数
function smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector)
% 计算B样条曲线上的一系列点
% 参数:
%   ctrls_pos: 控制点坐标，每列是一个控制点的[x;y]坐标
%   k: B样条次数
%   NodeVector: 节点矢量

% 设置采样点数量
num_samples = 100;
u_values = linspace(0, 1, num_samples);

% 初始化结果数组
smooth_points = zeros(2, num_samples);

% 计算每个参数值对应的点
for idx = 1:num_samples
    u = u_values(idx);
    smooth_points(:, idx) = computeBsplinePoint(ctrls_pos, u, k, NodeVector);
end
end

function p = computeBsplinePoint(ctrls_pos, u, k, NodeVector)
% 计算B样条上参数u对应的点
n = size(ctrls_pos, 2) - 1;
Nik = zeros(n+1, 1);

for i1 = 0:n
    Nik(i1+1, 1) = BaseFunction(i1+1, k, u, NodeVector);
end

p = ctrls_pos * Nik; % 计算样条上的点
end

function val = BaseFunction(i, k, u, NodeVector)
% B样条基函数
% i为控制点序号；k为B样条次数；u为样条参数；NodeVector为节点矢量
if k==0    
    if abs(u-NodeVector(end))<10^-6
        if NodeVector(i)<=u && abs(u-NodeVector(i+1))<10^-6
            val=1;return;
        else
            val=0;return;
        end
    else
        if(u<NodeVector(i)||u>=NodeVector(i+1))
            val=0;return;
        else
            val=1;return;
        end
    end
end

if k>0
    if(u<NodeVector(i)||u>NodeVector(i+k+1)) 
        val=0;
    else
        dtemp=NodeVector(i+k)-NodeVector(i);
        if(dtemp==0) 
            alpha=0;
        else
            alpha=(u-NodeVector(i))/dtemp;
        end
        dtemp=NodeVector(i+k+1)-NodeVector(i+1);
        if(dtemp==0) 
            beta=0;
        else
            beta=(NodeVector(i+k+1)-u)/dtemp;
        end
        val1=alpha*BaseFunction(i, k-1, u, NodeVector);
        val2=beta*BaseFunction(i+1, k-1, u, NodeVector);
        val=val1+val2;
    end
end
end 