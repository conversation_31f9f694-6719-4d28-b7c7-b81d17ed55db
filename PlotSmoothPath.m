function PlotSmoothPath()
% 光顺路径点计算与显示
% 基于B样条曲线计算光顺后的路径点并进行可视化显示

%% 读取输入数据
disp('请选择包含路径数据的文件（.txt 格式）：');
[filename_load, pathname_load] = uigetfile('*.txt', '选择路径文件');

if filename_load == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath = fullfile(pathname_load, filename_load);

% 打开文件
fileID = fopen(fullPath, 'r');
if fileID == -1
    disp('无法打开文件');
    return;
end

% 初始化 points 数组
points = [];

% 按行读取文件内容，并解析坐标数据
while ~feof(fileID)
    % 读取一行数据
    line = fgetl(fileID);
    if ischar(line) % 确保读取到的是有效行
        % 使用正则表达式提取X和Y的数值
        tokens = regexp(line, 'X([-\d.]+)\s+Y([-\d.]+)', 'tokens');
        
        if ~isempty(tokens)
            % 将提取的字符串转换为数值
            x = str2double(tokens{1}{1});
            y = str2double(tokens{1}{2});
            
            % 将坐标添加到 points 数组中
            points = [points; x, y];
        end
    end
end

% 关闭文件
fclose(fileID);

% 检查 points 是否成功读取
if isempty(points)
    disp('文件中没有有效坐标数据');
    return;
else
    disp('文件读取成功');
    disp('提取的坐标点:');
    disp(points);
end

%% 设置光顺参数
pe = input('请输入路径光顺的最大逼近误差 (默认值 0.03)：');
if isempty(pe)  % 如果用户直接按回车，使用默认值
    pe = 0.03;
end

%% 路径分析
points = points';
num_points = size(points, 2);
num = num_points - 2; % 过渡拐角数量

% 计算路径段长度
Length = sqrt(diff(points(1,:)).^2 + diff(points(2,:)).^2);

% 计算拐角处的夹角
angle = zeros(1, num);
for i = 1:num
    v1 = points(:, i) - points(:, i+1);
    v2 = points(:, i+2) - points(:, i+1);
    
    % 计算两个向量之间的夹角（弧度）
    cos_angle = (v1' * v2) / (norm(v1) * norm(v2));
    % 确保cos_angle在[-1, 1]范围内，避免数值误差
    cos_angle = max(min(cos_angle, 1), -1);
    angle(i) = acos(cos_angle);
end

% 将弧度转换为角度
angle_degrees = angle * 180 / pi;
disp('拐角角度（度）:');
disp(angle_degrees);

%% 计算光顺路径点
% 创建图形窗口
figure('Name', '路径光顺可视化', 'NumberTitle', 'off', 'Position', [100, 100, 800, 600]);

% 绘制原始路径
plot(points(1,:), points(2,:), 'b-', 'LineWidth', 1.5);
hold on;
plot(points(1,:), points(2,:), 'ro', 'MarkerSize', 6);
title('路径光顺可视化');
xlabel('X坐标');
ylabel('Y坐标');
grid on;
axis equal;

% 标记起点和终点
text(points(1,1), points(2,1), '  起点', 'FontSize', 12, 'Color', 'k');
text(points(1,end), points(2,end), '  终点', 'FontSize', 12, 'Color', 'k');

% 定义B样条参数
k = 5; % B样条次数，使用5次B样条

% 处理每个拐角
for i = 1:num
    % 只对角度小于等于170度的拐角进行光顺
    if angle_degrees(i) <= 170
        % 计算控制点和光顺参数D
        [ctrls_pos, D] = localSmoothing(pe, points, Length, angle, i);
        
        % 创建节点矢量 (均匀节点矢量)
        NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
        
        % 计算光顺曲线上的点
        smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector);
        
        % 绘制光顺曲线
        plot(smooth_points(1,:), smooth_points(2,:), 'g-', 'LineWidth', 2);
        
        % 绘制控制点和控制多边形 (可选)
        plot(ctrls_pos(1,:), ctrls_pos(2,:), 'mx--', 'LineWidth', 1);
        plot(ctrls_pos(1,:), ctrls_pos(2,:), 'mo', 'MarkerSize', 4);
        
        % 标记拐角点
        plot(points(1,i+1), points(2,i+1), 'gs', 'MarkerSize', 10, 'MarkerFaceColor', 'g');
    else
        % 标记不需要光顺的拐角点
        plot(points(1,i+1), points(2,i+1), 'bs', 'MarkerSize', 10, 'MarkerFaceColor', 'b');
    end
end

% 添加图例
legend({'原始路径', '路径点', '光顺曲线', '控制多边形', '控制点', '光顺拐角', '未光顺拐角'}, 'Location', 'best');

% 添加标题
title(sprintf('路径光顺可视化 (最大逼近误差: %.3f)', pe));

end

function [ctrls_pos, D] = localSmoothing(pe, mcs, Length, theta, i)
% 计算拐角处的控制点和光顺参数D
l1 = Length(1,i);
l2 = Length(1,i+1);
lim = 4*pe/(3*cos(0.5*theta(1,i)));
lp = min(lim, 0.2*min(l1, l2));
D = 2.5 * lp;

% 控制点求取，其中i用于记录第几组控制点
p1 = mcs(:, i);
p2 = mcs(:, i+1);
p3 = mcs(:, i+2);
v1 = p1 - p2; % 方向向量
v2 = p3 - p2;
L1 = sqrt(v1' * v1); % 直线刀具路径的长度
L2 = sqrt(v2' * v2);
m1 = v1 / L1; % 单位向量
m2 = v2 / L2;
ctrls_pos(:, 4) = p2;
ctrls_pos(:, 3) = p2 + m1 * lp;
ctrls_pos(:, 5) = p2 + m2 * lp;
ctrls_pos(:, 2) = ctrls_pos(:, 3)*2 - p2;
ctrls_pos(:, 1) = 0.5 * (ctrls_pos(:, 3)*5 - p2*3);
ctrls_pos(:, 6) = ctrls_pos(:, 5)*2 - p2;
ctrls_pos(:, 7) = 0.5 * (ctrls_pos(:, 5)*5 - p2*3);
end

function smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector)
% 计算B样条曲线上的一系列点
% 参数:
%   ctrls_pos: 控制点坐标，每列是一个控制点的[x;y]坐标
%   k: B样条次数
%   NodeVector: 节点矢量

% 设置采样点数量
num_samples = 100;
u_values = linspace(0, 1, num_samples);

% 初始化结果数组
smooth_points = zeros(2, num_samples);

% 计算每个参数值对应的点
for idx = 1:num_samples
    u = u_values(idx);
    smooth_points(:, idx) = computeBsplinePoint(ctrls_pos, u, k, NodeVector);
end
end

function p = computeBsplinePoint(ctrls_pos, u, k, NodeVector)
% 计算B样条上参数u对应的点
n = size(ctrls_pos, 2) - 1;
Nik = zeros(n+1, 1);

for i1 = 0:n
    Nik(i1+1, 1) = BaseFunction(i1+1, k, u, NodeVector);
end

p = ctrls_pos * Nik; % 计算样条上的点
end

function val = BaseFunction(i, k, u, NodeVector)
% B样条基函数
% i为控制点序号；k为B样条次数；u为样条参数；NodeVector为节点矢量
if k==0    
    if abs(u-NodeVector(end))<10^-6
        if NodeVector(i)<=u && abs(u-NodeVector(i+1))<10^-6
            val=1;return;
        else
            val=0;return;
        end
    else
        if(u<NodeVector(i)||u>=NodeVector(i+1))
            val=0;return;
        else
            val=1;return;
        end
    end
end

if k>0
    if(u<NodeVector(i)||u>NodeVector(i+k+1)) 
        val=0;
    else
        dtemp=NodeVector(i+k)-NodeVector(i);
        if(dtemp==0) 
            alpha=0;
        else
            alpha=(u-NodeVector(i))/dtemp;
        end
        dtemp=NodeVector(i+k+1)-NodeVector(i+1);
        if(dtemp==0) 
            beta=0;
        else
            beta=(NodeVector(i+k+1)-u)/dtemp;
        end
        val1=alpha*BaseFunction(i, k-1, u, NodeVector);
        val2=beta*BaseFunction(i+1, k-1, u, NodeVector);
        val=val1+val2;
    end
end
end 