function CompareThreePaths()
% 在同一坐标轴下比较三种不同控制策略的机床实际路径和光顺路径
% 对比G50输出_splin5_cv、G50输出_SPLINE5、Gcode_spline_5_min三个文件夹下的数据

clear all;
close all;

%% 设置字体大小参数
titleFontSize = 16;       % 标题字体大小
axisFontSize = 14;        % 坐标轴标签字体大小
annotationFontSize = 12;  % 误差标注字体大小
legendFontSize = 12;      % 图例字体大小

%% 第一步：读取第一个机床实际路径数据（G50输出_splin5_cv文件夹）
disp('请选择G50输出_splin5_cv文件夹中的机床实际路径数据文件（.txt 格式）：');
[filename_machine1, pathname_machine1] = uigetfile('G50输出_splin5_cv/*.txt', '选择第一个机床实际路径数据');

if filename_machine1 == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath_machine1 = fullfile(pathname_machine1, filename_machine1);

% 读取第一个机床数据文件
[x_machine1, y_machine1] = readMachineData(fullPath_machine1);
if isempty(x_machine1) || isempty(y_machine1)
    return;
end

%% 第二步：读取第二个机床实际路径数据（G50输出_SPLINE5文件夹）
disp(' ');
disp('请选择G50输出_SPLINE5文件夹中的机床实际路径数据文件（.txt 格式）：');
[filename_machine2, pathname_machine2] = uigetfile('G50输出_SPLINE5/*.txt', '选择第二个机床实际路径数据');

if filename_machine2 == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath_machine2 = fullfile(pathname_machine2, filename_machine2);

% 读取第二个机床数据文件
[x_machine2, y_machine2] = readMachineData(fullPath_machine2);
if isempty(x_machine2) || isempty(y_machine2)
    return;
end

%% 第三步：读取第三个机床实际路径数据（Gcode_spline_5_min文件夹）
disp(' ');
disp('请选择Gcode_spline_5_min文件夹中的机床实际路径数据文件（.txt 格式）：');
[filename_machine3, pathname_machine3] = uigetfile('Gcode_spline_5_min/*.txt', '选择第三个机床实际路径数据');

if filename_machine3 == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath_machine3 = fullfile(pathname_machine3, filename_machine3);

% 读取第三个机床数据文件
[x_machine3, y_machine3] = readMachineData(fullPath_machine3);
if isempty(x_machine3) || isempty(y_machine3)
    return;
end

%% 第四步：读取G代码文件并生成光顺路径
disp(' ');
disp('请选择包含路径数据的G代码文件（.txt 格式）：');
[filename_gcode, pathname_gcode] = uigetfile('*.txt', '选择G代码文件');

if filename_gcode == 0
    disp('用户取消了文件选择');
    return;
end

% 获取文件的完整路径
fullPath_gcode = fullfile(pathname_gcode, filename_gcode);

% 读取G代码文件
[points, original_path_x, original_path_y] = readGCodeFile(fullPath_gcode);
if isempty(points)
    return;
end

%% 设置光顺参数
pe = input('请输入路径光顺的最大逼近误差 (默认值 0.03)：');
if isempty(pe)  % 如果用户直接按回车，使用默认值
    pe = 0.03;
end

%% 路径分析
num_points = size(points, 2);
num = num_points - 2; % 过渡拐角数量

% 计算路径段长度
Length = sqrt(diff(points(1,:)).^2 + diff(points(2,:)).^2);

% 计算拐角处的夹角
angle = zeros(1, num);
for i = 1:num
    v1 = points(:, i) - points(:, i+1);
    v2 = points(:, i+2) - points(:, i+1);
    
    % 计算两个向量之间的夹角（弧度）
    cos_angle = (v1' * v2) / (norm(v1) * norm(v2));
    % 确保cos_angle在[-1, 1]范围内，避免数值误差
    cos_angle = max(min(cos_angle, 1), -1);
    angle(i) = acos(cos_angle);
end

% 将弧度转换为角度
angle_degrees = angle * 180 / pi;
disp('拐角角度（度）:');
disp(angle_degrees);

%% 计算光顺路径点
% 定义B样条参数
k = 5; % B样条次数，使用5次B样条

% 初始化存储所有光顺段的点
all_smooth_points_x = [];
all_smooth_points_y = [];

% 处理每个拐角
for i = 1:num
    % 只对角度小于等于170度的拐角进行光顺
    if angle_degrees(i) <= 170
        % 计算控制点和光顺参数D
        [ctrls_pos, D] = localSmoothing(pe, points, Length, angle, i);
        
        % 创建节点矢量 (均匀节点矢量)
        NodeVector = [0 0 0 0 0 0 0.5 1 1 1 1 1 1];
        
        % 计算光顺曲线上的点
        smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector);
        
        % 收集所有光顺点
        all_smooth_points_x = [all_smooth_points_x, smooth_points(1,:)];
        all_smooth_points_y = [all_smooth_points_y, smooth_points(2,:)];
    end
end

%% 计算与拐点的距离
% 定义拐点坐标
corner_point = [100, 0]; % 拐点坐标

% 计算第一个机床实际路径上每个点到拐点的距离
machine1_distances = sqrt((x_machine1 - corner_point(1)).^2 + (y_machine1 - corner_point(2)).^2);
[min_machine1_dist, min_machine1_idx] = min(machine1_distances);

% 计算第二个机床实际路径上每个点到拐点的距离
machine2_distances = sqrt((x_machine2 - corner_point(1)).^2 + (y_machine2 - corner_point(2)).^2);
[min_machine2_dist, min_machine2_idx] = min(machine2_distances);

% 计算第三个机床实际路径上每个点到拐点的距离
machine3_distances = sqrt((x_machine3 - corner_point(1)).^2 + (y_machine3 - corner_point(2)).^2);
[min_machine3_dist, min_machine3_idx] = min(machine3_distances);

% 计算光顺路径上每个点到拐点的距离
smooth_distances = sqrt((all_smooth_points_x - corner_point(1)).^2 + (all_smooth_points_y - corner_point(2)).^2);
[min_smooth_dist, min_smooth_idx] = min(smooth_distances);

% 计算原始G代码路径上每个点到拐点的距离
gcode_distances = sqrt((original_path_x - corner_point(1)).^2 + (original_path_y - corner_point(2)).^2);
[min_gcode_dist, min_gcode_idx] = min(gcode_distances);

% 显示最小距离
disp(['机床路径1(splin5_cv)到拐点(100,0)的最小距离: ', num2str(min_machine1_dist)]);
disp(['机床路径2(SPLINE5)到拐点(100,0)的最小距离: ', num2str(min_machine2_dist)]);
disp(['机床路径3(spline_5_min)到拐点(100,0)的最小距离: ', num2str(min_machine3_dist)]);
disp(['B样条光顺路径到拐点(100,0)的最小距离: ', num2str(min_smooth_dist)]);
disp(['原始G代码路径到拐点(100,0)的最小距离: ', num2str(min_gcode_dist)]);

%% 绘制对比图
figure('Name', '路径对比', 'NumberTitle', 'off', 'Position', [100, 100, 1000, 800]);

% 绘制第一个机床实际路径
plot(x_machine1, y_machine1, 'r-', 'LineWidth', 1.5);
hold on;

% 绘制第二个机床实际路径
plot(x_machine2, y_machine2, 'b-', 'LineWidth', 1.5);

% 绘制第三个机床实际路径
plot(x_machine3, y_machine3, 'm-', 'LineWidth', 1.5);

% 绘制原始G代码路径
plot(original_path_x, original_path_y, 'k--', 'LineWidth', 1);

% 绘制B样条光顺路径
plot(all_smooth_points_x, all_smooth_points_y, 'g-', 'LineWidth', 1.5);

% 标记拐点
plot(corner_point(1), corner_point(2), 'ko', 'MarkerSize', 10, 'MarkerFaceColor', 'y');
text(corner_point(1)+0.1, corner_point(2)+0.1, '拐点(100,0)', 'FontSize', annotationFontSize);

% 标记各路径上距离拐点最近的点
plot(x_machine1(min_machine1_idx), y_machine1(min_machine1_idx), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
text(x_machine1(min_machine1_idx)+0.1, y_machine1(min_machine1_idx)+0.1, ...
    ['最小距离: ', num2str(min_machine1_dist)], 'FontSize', annotationFontSize, 'Color', 'r');

plot(x_machine2(min_machine2_idx), y_machine2(min_machine2_idx), 'bo', 'MarkerSize', 8, 'MarkerFaceColor', 'b');
text(x_machine2(min_machine2_idx)+0.1, y_machine2(min_machine2_idx)+0.1, ...
    ['最小距离: ', num2str(min_machine2_dist)], 'FontSize', annotationFontSize, 'Color', 'b');

plot(x_machine3(min_machine3_idx), y_machine3(min_machine3_idx), 'mo', 'MarkerSize', 8, 'MarkerFaceColor', 'm');
text(x_machine3(min_machine3_idx)+0.1, y_machine3(min_machine3_idx)+0.1, ...
    ['最小距离: ', num2str(min_machine3_dist)], 'FontSize', annotationFontSize, 'Color', 'm');

plot(all_smooth_points_x(min_smooth_idx), all_smooth_points_y(min_smooth_idx), 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'g');
text(all_smooth_points_x(min_smooth_idx)+0.1, all_smooth_points_y(min_smooth_idx)+0.1, ...
    ['最小距离: ', num2str(min_smooth_dist)], 'FontSize', annotationFontSize, 'Color', 'g');

plot(original_path_x(min_gcode_idx), original_path_y(min_gcode_idx), 'ko', 'MarkerSize', 8, 'MarkerFaceColor', 'k');
text(original_path_x(min_gcode_idx)+0.1, original_path_y(min_gcode_idx)+0.1, ...
    ['最小距离: ', num2str(min_gcode_dist)], 'FontSize', annotationFontSize, 'Color', 'k');

% 标记起点和终点
plot(original_path_x(1), original_path_y(1), 'ko', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
plot(original_path_x(end), original_path_y(end), 'ko', 'MarkerSize', 10, 'MarkerFaceColor', 'k');

% 添加图例
legend({'机床路径1(splin5_cv)', '机床路径2(SPLINE5)', '机床路径3(spline_5_min)', ...
        '原始G代码路径', 'B样条光顺路径', '拐点', ...
        '机床路径1最近点', '机床路径2最近点', '机床路径3最近点', ...
        '光顺路径最近点', 'G代码最近点', '起点/终点'}, ...
        'Location', 'best', 'FontSize', legendFontSize);

% 设置坐标轴等比例
axis equal;
grid on;
title(['三种控制策略路径对比'], 'FontSize', titleFontSize);
xlabel('X 坐标', 'FontSize', axisFontSize);
ylabel('Y 坐标', 'FontSize', axisFontSize);

% 设置坐标轴字体大小
set(gca, 'FontSize', axisFontSize-2);

% 添加信息框
info_text = sprintf('最大逼近误差: %.3f\n机床路径1点数: %d\n机床路径2点数: %d\n机床路径3点数: %d\nG代码路径点数: %d\n\n机床1到拐点最小距离: %.3f\n机床2到拐点最小距离: %.3f\n机床3到拐点最小距离: %.3f\n光顺到拐点最小距离: %.3f', ...
    pe, length(x_machine1), length(x_machine2), length(x_machine3), num_points, min_machine1_dist, min_machine2_dist, min_machine3_dist, min_smooth_dist);
annotation('textbox', [0.15, 0.02, 0.3, 0.15], 'String', info_text, ...
    'EdgeColor', 'none', 'HorizontalAlignment', 'left', 'FontSize', annotationFontSize);

% 保存图像
output_filename = ['三路径对比_', strrep(filename_gcode, '.txt', ''), '.png'];
saveas(gcf, output_filename);
fprintf('对比图已保存为%s\n', output_filename);

end

%% 读取机床数据文件函数
function [x_machine, y_machine] = readMachineData(fullPath)
% 读取机床数据文件
try
    machine_data = readtable(fullPath, 'HeaderLines', 2, 'Delimiter', ' ', 'MultipleDelimsAsOne', true);
    disp(['成功读取机床数据文件: ', fullPath]);
catch ME
    disp(['无法读取文件: ', fullPath]);
    disp(['错误信息: ', ME.message]);
    x_machine = [];
    y_machine = [];
    return;
end

% 获取列名
colNames = machine_data.Properties.VariableNames;
disp('可用的列名:');
disp(colNames);

% 尝试找到X和Y位置数据列
xColIndex = find(contains(colNames, 'X.fActPosition'));
yColIndex = find(contains(colNames, 'Y.fActPosition'));

% 如果找不到列，尝试手动指定
if isempty(xColIndex) || isempty(yColIndex)
    % 尝试手动指定列索引（根据数据文件中的位置）
    fprintf('未能自动找到X.fActPosition和Y.fActPosition列，尝试手动指定列...\n');
    
    % 根据Data.txt文件中的位置，X.fActPosition应该是第18列，Y.fActPosition是第19列
    xColIndex = 18;
    yColIndex = 19;
    
    fprintf('手动指定X.fActPosition为第%d列，Y.fActPosition为第%d列\n', xColIndex, yColIndex);
end

% 提取X和Y坐标
x_machine = table2array(machine_data(:, xColIndex));
y_machine = table2array(machine_data(:, yColIndex));

% 移除零值点（通常是机床未启动的数据）
valid_indices = (x_machine ~= 0) | (y_machine ~= 0);
x_machine = x_machine(valid_indices);
y_machine = y_machine(valid_indices);
end

%% 读取G代码文件函数
function [points, original_path_x, original_path_y] = readGCodeFile(fullPath)
% 打开文件
fileID = fopen(fullPath, 'r');
if fileID == -1
    disp('无法打开文件');
    points = [];
    original_path_x = [];
    original_path_y = [];
    return;
end

% 初始化 points 数组
points_array = [];

% 按行读取文件内容，并解析坐标数据
while ~feof(fileID)
    % 读取一行数据
    line = fgetl(fileID);
    if ischar(line) % 确保读取到的是有效行
        % 使用正则表达式提取X和Y的数值
        tokens = regexp(line, 'X([-\d.]+)\s+Y([-\d.]+)', 'tokens');
        
        if ~isempty(tokens)
            % 将提取的字符串转换为数值
            x = str2double(tokens{1}{1});
            y = str2double(tokens{1}{2});
            
            % 将坐标添加到 points 数组中
            points_array = [points_array; x, y];
        end
    end
end

% 关闭文件
fclose(fileID);

% 检查 points 是否成功读取
if isempty(points_array)
    disp('文件中没有有效坐标数据');
    points = [];
    original_path_x = [];
    original_path_y = [];
    return;
else
    disp('G代码文件读取成功');
    disp(['提取的坐标点数量: ', num2str(size(points_array, 1))]);
    
    % 转换为列向量格式
    points = points_array';
    original_path_x = points(1,:);
    original_path_y = points(2,:);
end
end

%% 局部光顺函数
function [ctrls_pos, D] = localSmoothing(pe, mcs, Length, theta, i)
% 计算拐角处的控制点和光顺参数D
l1 = Length(1,i);
l2 = Length(1,i+1);
lim = 4*pe/(3*cos(0.5*theta(1,i)));
lp = min(lim, 0.2*min(l1, l2));
D = 2.5 * lp;

% 控制点求取，其中i用于记录第几组控制点
p1 = mcs(:, i);
p2 = mcs(:, i+1);
p3 = mcs(:, i+2);
v1 = p1 - p2; % 方向向量
v2 = p3 - p2;
L1 = sqrt(v1' * v1); % 直线刀具路径的长度
L2 = sqrt(v2' * v2);
m1 = v1 / L1; % 单位向量
m2 = v2 / L2;
ctrls_pos(:, 4) = p2;
ctrls_pos(:, 3) = p2 + m1 * lp;
ctrls_pos(:, 5) = p2 + m2 * lp;
ctrls_pos(:, 2) = ctrls_pos(:, 3)*2 - p2;
ctrls_pos(:, 1) = 0.5 * (ctrls_pos(:, 3)*5 - p2*3);
ctrls_pos(:, 6) = ctrls_pos(:, 5)*2 - p2;
ctrls_pos(:, 7) = 0.5 * (ctrls_pos(:, 5)*5 - p2*3);
end

%% B样条曲线计算函数
function smooth_points = computeBsplinePoints(ctrls_pos, k, NodeVector)
% 计算B样条曲线上的一系列点
% 参数:
%   ctrls_pos: 控制点坐标，每列是一个控制点的[x;y]坐标
%   k: B样条次数
%   NodeVector: 节点矢量

% 设置采样点数量
num_samples = 100;
u_values = linspace(0, 1, num_samples);

% 初始化结果数组
smooth_points = zeros(2, num_samples);

% 计算每个参数值对应的点
for idx = 1:num_samples
    u = u_values(idx);
    smooth_points(:, idx) = computeBsplinePoint(ctrls_pos, u, k, NodeVector);
end
end

function p = computeBsplinePoint(ctrls_pos, u, k, NodeVector)
% 计算B样条上参数u对应的点
n = size(ctrls_pos, 2) - 1;
Nik = zeros(n+1, 1);

for i1 = 0:n
    Nik(i1+1, 1) = BaseFunction(i1+1, k, u, NodeVector);
end

p = ctrls_pos * Nik; % 计算样条上的点
end

function val = BaseFunction(i, k, u, NodeVector)
% B样条基函数
% i为控制点序号；k为B样条次数；u为样条参数；NodeVector为节点矢量
if k==0    
    if abs(u-NodeVector(end))<10^-6
        if NodeVector(i)<=u && abs(u-NodeVector(i+1))<10^-6
            val=1;return;
        else
            val=0;return;
        end
    else
        if(u<NodeVector(i)||u>=NodeVector(i+1))
            val=0;return;
        else
            val=1;return;
        end
    end
end

if k>0
    if(u<NodeVector(i)||u>NodeVector(i+k+1)) 
        val=0;
    else
        dtemp=NodeVector(i+k)-NodeVector(i);
        if(dtemp==0) 
            alpha=0;
        else
            alpha=(u-NodeVector(i))/dtemp;
        end
        dtemp=NodeVector(i+k+1)-NodeVector(i+1);
        if(dtemp==0) 
            beta=0;
        else
            beta=(NodeVector(i+k+1)-u)/dtemp;
        end
        val1=alpha*BaseFunction(i, k-1, u, NodeVector);
        val2=beta*BaseFunction(i+1, k-1, u, NodeVector);
        val=val1+val2;
    end
end
end 